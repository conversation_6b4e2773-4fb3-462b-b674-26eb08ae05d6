import torch
from torch import nn, Tensor
from torchvision.models import ResNet18_Weights, ResNet34_Weights, ResNet50_Weights

from typing import Any, Callable, List, Optional, Type, Tuple


__all__ = ["resnet18", "resnet34", "resnet50"]

import torch

from torch import nn
from torchvision.models import vgg16, VGG16_Weights, vgg19_bn, VGG19_BN_Weights


class SELayer(nn.Module):
    """
    Squeeze-and-Excitation layer for channel attention.

    This layer applies channel-wise attention by first squeezing spatial dimensions
    to get global channel statistics, then exciting (re-weighting) channels based
    on their importance.

    Reference: https://github.com/moskomule/senet.pytorch/blob/master/senet/se_module.py

    Args:
        channel: Number of input channels
        reduction: Reduction ratio for the bottleneck layer (default: 16)
    """

    def __init__(self, channel: int, reduction: int = 16):
        super(SELayer, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)  # Squeeze: global average pooling
        self.fc = nn.Sequential(  # Excitation: channel attention mechanism
            nn.Linear(channel, channel // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channel // reduction, channel, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of the Squeeze-and-Excitation layer.

        Args:
            x: Input tensor of shape (batch_size, channels, height, width)

        Returns:
            Output tensor with channel-wise attention applied
        """
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)


class FinalModel(nn.Module):
    """
    Multi-input neural network for gaze tracking.

    This model processes face and eye images simultaneously using separate CNN branches
    and combines their features for final gaze prediction (pitch and yaw angles).

    Architecture:
    - Face processing branch: VGG16-based CNN with dilated convolutions
    - Eye processing branch: VGG16-based CNN with Squeeze-and-Excitation attention
    - Feature fusion: Concatenation followed by fully connected layers
    """

    def __init__(self, use_vgg16: bool = True, *args, **kwargs):
        """
        Initialize the gaze tracking model.

        Args:
            use_vgg16: Whether to use VGG16 (True) or VGG19_BN (False) as backbone
        """
        super().__init__(*args, **kwargs)

        # Subject-specific biases (currently disabled)
        # self.subject_biases = nn.Parameter(torch.zeros(15 * 2, 2))

        if use_vgg16:
            # First four convolutional layers of VGG16 pretrained on ImageNet
            backbone = vgg16(weights=VGG16_Weights.IMAGENET1K_V1).features[:9]
        else:
            # First four convolutional layers of VGG19_BN pretrained on ImageNet
            backbone = vgg19_bn(weights=VGG19_BN_Weights.IMAGENET1K_V1).features[:13]

        self.cnn_face = nn.Sequential(
            backbone,
            nn.Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1), padding='same'),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(2, 2)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(3, 3)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 128, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(5, 5)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(128),
            nn.Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(11, 11)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(128),
        )

        self.cnn_eye = nn.Sequential(
            backbone,
            nn.Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1), padding='same'),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(2, 2)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(3, 3)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 128, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(4, 5)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(128),
            nn.Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(5, 11)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(128),
        )

        self.fc_face = nn.Sequential(
            nn.Flatten(),
            nn.Linear(6 * 6 * 128, 256),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(256),
            nn.Linear(256, 64),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(64),
        )

        self.cnn_eye2fc = nn.Sequential(
            SELayer(256),

            nn.Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding='same'),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(256),

            SELayer(256),

            nn.Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding='same'),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(128),

            SELayer(128),
        )

        self.fc_eye = nn.Sequential(
            nn.Flatten(),
            nn.Linear(4 * 6 * 128, 512),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(512),
        )

        self.fc_eyes_face = nn.Sequential(
            nn.Dropout(p=0.5),
            nn.Linear(576, 256),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(256),
            nn.Dropout(p=0.5),
            nn.Linear(256, 2),
        )

    def forward(self,
                person_idx: torch.Tensor,
                full_face: torch.Tensor,
                right_eye: torch.Tensor,
                left_eye: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of the gaze tracking model.

        Args:
            person_idx: Person indices for subject-specific biases (currently unused)
            full_face: Face images of shape (batch_size, 3, 96, 96)
            right_eye: Right eye images of shape (batch_size, 3, 64, 96)
            left_eye: Left eye images of shape (batch_size, 3, 64, 96)

        Returns:
            Predicted gaze angles (pitch, yaw) of shape (batch_size, 2)
        """
        # Process face image
        out_cnn_face = self.cnn_face(full_face)
        out_fc_face = self.fc_face(out_cnn_face)

        # Process eye images separately
        out_cnn_right_eye = self.cnn_eye(right_eye)
        out_cnn_left_eye = self.cnn_eye(left_eye)
        out_cnn_eye = torch.cat((out_cnn_right_eye, out_cnn_left_eye), dim=1)

        # Apply attention and feature fusion for eyes
        cnn_eye2fc_out = self.cnn_eye2fc(out_cnn_eye)
        out_fc_eye = self.fc_eye(cnn_eye2fc_out)

        # Combine face and eye features
        fc_concatenated = torch.cat((out_fc_face, out_fc_eye), dim=1)
        gaze_prediction = self.fc_eyes_face(fc_concatenated)

        # Subject-specific biases are currently disabled
        # return gaze_prediction + self.subject_biases[person_idx].squeeze(1)
        return gaze_prediction


if __name__ == '__main__':
    model = FinalModel()

    # Quick inspect the face‐CNN submodule
    print(model.cnn_face)

    # Use torchinfo.summary to print the full model topology
    batch_size = 16
    summary(
        model,
        input_size=[
            (batch_size, 1),            # person_idx as a long tensor
            (batch_size, 3, 96, 96),    # full_face
            (batch_size, 3, 64, 96),    # right_eye
            (batch_size, 3, 64, 96)     # left_eye
        ],
        dtypes=[torch.long, torch.float32, torch.float32, torch.float32],
        col_names=("input_size", "output_size", "num_params", "trainable")
    )



def conv3x3(in_channels: int, out_channels: int, stride: int = 1, groups: int = 1, dilation: int = 1) -> nn.Conv2d:
    """3x3 convolution with padding"""
    return nn.Conv2d(
        in_channels,
        out_channels,
        kernel_size=3,
        stride=stride,
        padding=dilation,
        groups=groups,
        bias=False,
        dilation=dilation,
    )


def conv1x1(in_channels: int, out_channels: int, stride: int = 1) -> nn.Conv2d:
    """1x1 convolution"""
    return nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=stride, bias=False)


class BasicBlock(nn.Module):
    expansion: int = 1

    def __init__(
            self,
            in_channels: int,
            out_channels: int,
            stride: int = 1,
            downsample: Optional[nn.Module] = None,
            groups: int = 1,
            base_width: int = 64,
            dilation: int = 1,
            norm_layer: Optional[Callable[..., nn.Module]] = None,
    ) -> None:
        super().__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        if groups != 1 or base_width != 64:
            raise ValueError("BasicBlock only supports groups=1 and base_width=64")
        if dilation > 1:
            raise NotImplementedError("Dilation > 1 not supported in BasicBlock")
        # Both self.conv1 and self.downsample layers downsample the input when stride != 1
        self.conv1 = conv3x3(in_channels, out_channels, stride)
        self.bn1 = norm_layer(out_channels)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = conv3x3(out_channels, out_channels)
        self.bn2 = norm_layer(out_channels)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x: Tensor) -> Tensor:
        identity = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)

        if self.downsample is not None:
            identity = self.downsample(x)

        out += identity
        out = self.relu(out)

        return out


class Bottleneck(nn.Module):
    expansion: int = 4

    def __init__(
        self,
        inplanes: int,
        planes: int,
        stride: int = 1,
        downsample: Optional[nn.Module] = None,
        groups: int = 1,
        base_width: int = 64,
        dilation: int = 1,
        norm_layer: Optional[Callable[..., nn.Module]] = None,
    ) -> None:
        super().__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        width = int(planes * (base_width / 64.0)) * groups
        # Both self.conv2 and self.downsample layers downsample the input when stride != 1
        self.conv1 = conv1x1(inplanes, width)
        self.bn1 = norm_layer(width)
        self.conv2 = conv3x3(width, width, stride, groups, dilation)
        self.bn2 = norm_layer(width)
        self.conv3 = conv1x1(width, planes * self.expansion)
        self.bn3 = norm_layer(planes * self.expansion)
        self.relu = nn.ReLU(inplace=True)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x: Tensor) -> Tensor:
        identity = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)
        out = self.relu(out)

        out = self.conv3(out)
        out = self.bn3(out)

        if self.downsample is not None:
            identity = self.downsample(x)

        out += identity
        out = self.relu(out)

        return out


class ResNet(nn.Module):
    def __init__(
            self,
            block: Type[BasicBlock | Bottleneck],
            layers: List[int],
            num_classes: int = 1000,
            groups: int = 1,
            width_per_group: int = 64,
            replace_stride_with_dilation: Optional[List[bool]] = None,
            norm_layer: Optional[Callable[..., nn.Module]] = None,
    ) -> None:
        super().__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        self._norm_layer = norm_layer

        self.in_channels = 64
        self.dilation = 1
        if replace_stride_with_dilation is None:
            # each element in the tuple indicates if we should replace
            # the 2x2 stride with a dilated convolution instead
            replace_stride_with_dilation = [False, False, False]
        if len(replace_stride_with_dilation) != 3:
            raise ValueError(
                "replace_stride_with_dilation should be None "
                f"or a 3-element tuple, got {replace_stride_with_dilation}"
            )
        self.groups = groups
        self.base_width = width_per_group
        self.conv1 = nn.Conv2d(3, self.in_channels, kernel_size=7, stride=2, padding=3, bias=False)
        self.bn1 = norm_layer(self.in_channels)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)
        self.layer1 = self._make_layer(block, 64, layers[0])
        self.layer2 = self._make_layer(block, 128, layers[1], stride=2, dilate=replace_stride_with_dilation[0])
        self.layer3 = self._make_layer(block, 256, layers[2], stride=2, dilate=replace_stride_with_dilation[1])
        self.layer4 = self._make_layer(block, 512, layers[3], stride=2, dilate=replace_stride_with_dilation[2])
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))

        # yaw and pitch
        self.fc_yaw = nn.Linear(512 * block.expansion, num_classes)
        self.fc_pitch = nn.Linear(512 * block.expansion, num_classes)

        # left and right eye coordinates (x, y for each eye)
        # self.fc_left_eye = nn.Linear(512 * block.expansion, 2)
        # self.fc_right_eye = nn.Linear(512 * block.expansion, 2)

        # Original FC Layer for ResNet
        # self.fc = nn.Linear(512 * block.expansion, num_classes)

        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode="fan_out", nonlinearity="relu")
            elif isinstance(m, (nn.BatchNorm2d, nn.GroupNorm)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def _make_layer(
            self,
            block: Type[BasicBlock | Bottleneck],
            planes: int,
            blocks: int,
            stride: int = 1,
            dilate: bool = False,
    ) -> nn.Sequential:
        norm_layer = self._norm_layer
        downsample = None
        previous_dilation = self.dilation
        if dilate:
            self.dilation *= stride
            stride = 1
        if stride != 1 or self.in_channels != planes * block.expansion:
            downsample = nn.Sequential(
                conv1x1(self.in_channels, planes * block.expansion, stride),
                norm_layer(planes * block.expansion),
            )

        layers = []
        layers.append(
            block(
                self.in_channels,
                planes,
                stride,
                downsample,
                self.groups,
                self.base_width,
                previous_dilation,
                norm_layer
            )
        )
        self.in_channels = planes * block.expansion
        for _ in range(1, blocks):
            layers.append(
                block(
                    self.in_channels,
                    planes,
                    groups=self.groups,
                    base_width=self.base_width,
                    dilation=self.dilation,
                    norm_layer=norm_layer,
                )
            )

        return nn.Sequential(*layers)

    def forward(self, x: Tensor) -> Tuple[Tensor, Tensor, Tensor, Tensor]:
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)

        x = self.layer1(x)  # 1/4
        x = self.layer2(x)  # 1/8
        x = self.layer3(x)  # 1/16
        x = self.layer4(x)  # 1/32

        x = self.avgpool(x)
        x = torch.flatten(x, 1)

        # Original FC Layer for ResNet
        # x = self.fc(x)
        yaw = self.fc_yaw(x)
        pitch = self.fc_pitch(x)
        # left_eye = self.fc_left_eye(x)
        # right_eye = self.fc_right_eye(x)

        return pitch, yaw#, left_eye, right_eye


def load_filtered_state_dict(model, state_dict):
    """Update the model's state dictionary with filtered parameters.

    Args:
        model: The model instance to update (must have `state_dict` and `load_state_dict` methods).
        state_dict: A dictionary of parameters to load into the model.
    """
    current_model_dict = model.state_dict()
    filtered_state_dict = {key: value for key, value in state_dict.items() if key in current_model_dict}
    current_model_dict.update(filtered_state_dict)
    model.load_state_dict(current_model_dict)


def _resnet(block: Type[BasicBlock], layers: List[int], weights: Optional[ResNet34_Weights], progress: bool, **kwargs: Any) -> ResNet:
    model = ResNet(block, layers, **kwargs)

    if weights is not None:
        state_dict = weights.get_state_dict(progress=progress, check_hash=True)
        load_filtered_state_dict(model, state_dict)

    return model


def resnet18(*, pretrained: bool = True, progress: bool = True, **kwargs: Any) -> ResNet:
    if pretrained:
        weights = ResNet18_Weights.DEFAULT
    else:
        weights = None
    return _resnet(BasicBlock, [2, 2, 2, 2], weights, progress, **kwargs)


def resnet34(*, pretrained: bool = True, progress: bool = True, **kwargs: Any) -> ResNet:
    if pretrained:
        weights = ResNet34_Weights.DEFAULT
    else:
        weights = None
    return _resnet(BasicBlock, [3, 4, 6, 3], weights, progress, **kwargs)


def resnet50(*, pretrained: bool = True, progress: bool = True, **kwargs: Any) -> ResNet:
    if pretrained:
        weights = ResNet50_Weights.DEFAULT
    else:
        weights = None

    return _resnet(Bottleneck, [3, 4, 6, 3], weights, progress, **kwargs)
