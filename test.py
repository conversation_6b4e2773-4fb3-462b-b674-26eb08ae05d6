import os
import argparse
import logging
import warnings
import numpy as np
from tqdm import tqdm

import torch
import torch.nn.functional as F

from utils import util
from config import data_config
from utils.datasets_v2 import get_dataloaders
from utils.helpers import angular_error, get_model, pitchyaw_to_gaze_vector


util.setup_multi_processes()
util.init_deterministic_seed()

warnings.filterwarnings("ignore")
# Setup logging
logging.basicConfig(level=logging.INFO, format='%(message)s')


def parse_args():
    """Parse input arguments."""
    parser = argparse.ArgumentParser(description="Gaze estimation evaluation")
    parser.add_argument("--data", type=str, default="data/dataset_normalized_v2", help="Directory path for gaze images.")
    parser.add_argument("--data-config", type=str, default="max", help="Dataset name, available `gaze360`, `mpiigaze`, `max`.")
    parser.add_argument("--weight", type=str, default="output/max_resnet18_1748497749/best_model.pt", help="Path to model weight for evaluation.")
    parser.add_argument("--batch-size", type=int, default=16, help="Batch size.")
    parser.add_argument("--arch", type=str, default="resnet18",
                        help="Network architecture, currently available: resnet18/34/50, mobilenetv2, mobileone_s0-s4.")
    parser.add_argument("--num-workers", type=int, default=8, help="Number of workers for data loading.")
    args = parser.parse_args()

    # Override default values based on selected dataset
    if args.data_config in data_config:
        dataset_config = data_config[args.data_config]
        args.bins = dataset_config["bins"]
        args.binwidth = dataset_config["binwidth"]
        args.angle = dataset_config["angle"]
    else:
        raise ValueError(f"Unknown dataset: {args.data_config}. Available options: {list(data_config.keys())}")

    return args


@torch.no_grad()
def evaluate(args, model, data_loader, idx_tensor, device, mode="val"):
    """
    Evaluate the model on the test dataset.

    Args:
        args (argparse.Namespace): Parsed command-line arguments.
        model (nn.Module): The gaze estimation model.
        data_loader (torch.utils.data.DataLoader): DataLoader for the test dataset.
        idx_tensor (torch.Tensor): Tensor representing bin indices.
        device (torch.device): Device to perform evaluation on.
    """
    model.eval()
    total_num_samples = 0
    sum_angular_error = 0
    num_batches = len(data_loader)

    for images, binned_labels, regression_labels in tqdm(data_loader, total=num_batches):
        images = images.to(device)

        # Regression labels
        regression_label_pitch = np.radians(regression_labels[:, 0].cpu().numpy(), dtype=np.float32)
        regression_label_yaw = np.radians(regression_labels[:, 1].cpu().numpy(), dtype=np.float32)

        # Inference
        binned_pitch, binned_yaw = model(images)

        # Regression predictions
        binned_pitch = F.softmax(binned_pitch, dim=1)
        binned_yaw = F.softmax(binned_yaw, dim=1)

        # Mapping from binned (0 to 90) to angles (-180° to 180°) or (0 to 28) to angles (-42°, 42°)
        pitch = torch.sum(binned_pitch * idx_tensor, 1) * args.binwidth - args.angle
        yaw = torch.sum(binned_yaw * idx_tensor, 1) * args.binwidth - args.angle

        pitch = np.radians(pitch.cpu().numpy())
        yaw = np.radians(yaw.cpu().numpy())

        total_num_samples += images.size(0)
        for p, y, pl, yl in zip(pitch, yaw, regression_label_pitch, regression_label_yaw):
            sum_angular_error += angular_error(
                pitchyaw_to_gaze_vector(p, y), 
                pitchyaw_to_gaze_vector(pl, yl)
            )

    avg_angular_error = sum_angular_error / total_num_samples
    
    if mode == "test":
        logging.info(
            f"Total Number of Samples: {total_num_samples} | "
            f"Angular Vector Error: {avg_angular_error:.7f}°"
        )
    
    return avg_angular_error


def main():
    args = parse_args()

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    torch.backends.cudnn.benchmark = False

    model = get_model(args.arch, args.bins, inference_mode=True)

    if os.path.exists(args.weight):
        model.load_state_dict(torch.load(args.weight, map_location=device, weights_only=True))
    else:
        raise ValueError(f"Model weight not found at {args.weight}")
    
    model.to(device)
    
    # Load dataloaders with configurable augmentation
    train_loader, val_loader, test_loader = get_dataloaders(args)

    idx_tensor = torch.arange(args.bins, device=device, dtype=torch.float32)

    logging.info("Start Evaluation")
    evaluate(args, model, test_loader, idx_tensor, device, mode="test")


if __name__ == '__main__':
    main()
