import os
import argparse
import time
import copy
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torchvision import transforms
import torch.backends.cudnn as cudnn
from torch.optim.lr_scheduler import CosineAnnealingLR
from l2cs import L2CS_ViT, Mpiigaze, L2CS_EfficientViT
from tensorboardX import SummaryWriter
import pandas as pd
import json
import math

def parse_args():
    parser = argparse.ArgumentParser(description="Gaze estimation using L2CSNet")

    parser.add_argument(
        "--gazeMpiimage_dir",
        dest="gazeMpiimage_dir",
        help="Directory path for gaze images",
        default="datasets/MPIIFaceGaze/Image",
        type=str,
    )

    parser.add_argument(
        "--gazeMpiilabel_dir",
        dest="gazeMpiilabel_dir",
        help="Directory path for gaze labels",
        default="datasets/MPIIFaceGaze/Label",
        type=str,
    )

    parser.add_argument(
        "--dataset",
        dest="dataset",
        help="mpiigaze, rtgene, gaze360, ethgaze",
        default="mpiigaze",
        type=str,
    )

    parser.add_argument(
        "--output",
        dest="output",
        help="Path of output models",
        default="output/snapshots_PAngular_1GPU/",
        type=str,
    )

    parser.add_argument(
        "--gpu",
        dest="gpu_id",
        help="GPU device id to use [0] or multiple 0,1,2,3",
        default="1",
        type=str,
    )

    parser.add_argument(
        "--num_epochs",
        dest="num_epochs",
        help="Maximum number of training epochs",
        default=1,
        type=int,
    )

    parser.add_argument(
        "--batch_size",
        dest="batch_size",
        help="Batch Size",
        default=32,
        type=int
    )

    parser.add_argument(
        "--arch",
        dest="arch",
        help="Network architecture, can be: L2CS_ViT, ResNet18, ResNet34, ...",
        default="L2CS_ViT",
        type=str,
    )

    parser.add_argument(
        "--alpha",
        dest="alpha",
        help="Regression loss coefficient",
        default=1,
        type=float,
    )

    parser.add_argument(
        "--lr",
        dest="lr",
        help="Base learning rate",
        default=0.0001,
        type=float
    )

    parser.add_argument(
        "--patience",
        dest="patience",
        help="Patience for early stopping",
        default=10,
        type=int
    )

    parser.add_argument(
        "--threshold",
        dest="threshold",
        help="Threshold for retraining on best model weights",
        default=0.01,
        type=float
    )

    args = parser.parse_args()
    return args

def get_model(arch, bins):
    if arch == "L2CS_ViT":
        image_size = 448
        patch_size = 16
        num_classes = bins
        dim = 512
        depth = 6
        heads = 8
        mlp_dim = 1024
        model = L2CS_ViT(
            image_size, patch_size, num_classes, dim, depth, heads, mlp_dim
        )
        model_params = {
            'image_size': image_size,
            'patch_size': patch_size,
            'num_classes': num_classes,
            'dim': dim,
            'depth': depth,
            'heads': heads,
            'mlp_dim': mlp_dim
        }
    elif arch == "L2CS_EfficientViT":
        model = L2CS_EfficientViT(num_classes = bins)
        model_params = {
            'num_classes': bins
        }
    else:
        assert False, "No Valid model architecture is passed." + arch 
    return model, model_params

def spherical2cartesial(x):
    output = torch.zeros(x.size(0), 3)
    output[:, 2] = -torch.cos(x[:, 1]) * torch.cos(x[:, 0])
    output[:, 0] = torch.cos(x[:, 1]) * torch.sin(x[:, 0])
    output[:, 1] = torch.sin(x[:, 1])
    return output

def compute_angular_error(input, target):
    input = spherical2cartesial(input)
    target = spherical2cartesial(target)
    input = input.view(-1, 3, 1)
    target = target.view(-1, 1, 3)
    output_dot = torch.bmm(target, input)
    output_dot = output_dot.view(-1)
    output_dot = torch.acos(output_dot)
    output_dot = output_dot.data
    output_dot = 180 * torch.mean(output_dot) / math.pi
    return output_dot

# def LR_weights_callback(model, best_model_weights, optimizer, val_mae, best_mae, current_lr, new_lr, fail_epochs):
#     """ Callback weights and decrease LR """
#     if val_mae < best_mae:
#         fail_epochs = 0
#         best_mae = val_mae
#         best_model_weights = copy.deepcopy(model.state_dict())
#     elif fail_epochs + 1 >= 3 and current_lr > 1e-4:
#         fail_epochs = 0
#         model.load_state_dict(best_model_weights)
#         optimizer.param_groups[0]['lr'] = max(new_lr * 0.7, 1e-6)
#         new_lr = optimizer.param_groups[0]['lr']
#     elif fail_epochs + 1 >= 10 and current_lr > 1e-5:
#         fail_epochs = 0
#         optimizer.param_groups[0]['lr'] = max(new_lr * 0.7, 1e-6)
#         new_lr = optimizer.param_groups[0]['lr']
#     elif fail_epochs + 1 >= 15 and current_lr <= 1e-5:
#         fail_epochs = 0
#         optimizer.param_groups[0]['lr'] = max(new_lr * 0.7, 1e-6)
#         new_lr = optimizer.param_groups[0]['lr']
#     else:
#         fail_epochs += 1

#     return best_model_weights, best_mae, new_lr, fail_epochs

if __name__ == "__main__":
    """
    This training pipeline saves all necessary training realated hyperparameters, 
    Gives loss /angular metrics exactly used by the author in the paper. 
    Save experiments weights last + best
    For each learning rate update, use best weight for training.

    """


    args = parse_args()
    cudnn.enabled = True
    num_epochs = args.num_epochs
    batch_size = args.batch_size
    data_set = args.dataset
    alpha = args.alpha
    output = args.output
    patience = args.patience
    threshold = args.threshold

    transformations = transforms.Compose(
        [
            transforms.Resize(448),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ]
    )

    assert data_set == "mpiigaze"
    folder = os.listdir(args.gazeMpiilabel_dir)
    folder.sort()
    testlabelpathombined = [os.path.join(args.gazeMpiilabel_dir, j) for j in folder]
    device = "cuda:" + args.gpu_id

    writer = SummaryWriter(logdir=output)

    for fold in range(15):
        model, model_params = get_model(args.arch, 28)
        model = model.to(device)
        train_dataset = Mpiigaze(testlabelpathombined, args.gazeMpiimage_dir, transformations, True, fold=fold)
        val_dataset = Mpiigaze(testlabelpathombined, args.gazeMpiimage_dir, transformations, False, fold=fold)

        train_loader_gaze = DataLoader(
            dataset=train_dataset,
            batch_size=int(batch_size),
            shuffle=True,
            num_workers=4,
            pin_memory=True
        )

        val_loader_gaze = DataLoader(
            dataset=val_dataset,
            batch_size=int(batch_size),
            shuffle=False,
            num_workers=4,
            pin_memory=True
        )

        optimizer_gaze = torch.optim.AdamW(
            model.parameters(),
            lr=args.lr,
            weight_decay=1e-2,
            betas=(0.9, 0.999),
            eps=1e-8
        )

        scheduler_gaze = CosineAnnealingLR(optimizer_gaze, T_max=10, eta_min=1e-6)

        summary_name = "{}_{}".format("L2CS-mpiigaze", int(time.time()))
        summary_path = os.path.join(output, "{}".format(summary_name), "fold" + str(fold))
        os.makedirs(summary_path, exist_ok=True)

        criterion = nn.CrossEntropyLoss().to(device)
        reg_criterion = nn.MSELoss().to(device)
        softmax = nn.Softmax(dim=1).to(device)
        idx_tensor = torch.FloatTensor([idx for idx in range(28)]).to(device)

        configuration = f"\ntrain configuration, gpu_id={args.gpu_id}, batch_size={batch_size}, model_arch={args.arch}\n Start training dataset={data_set}, loader={len(train_loader_gaze)}, fold={fold}--------------\n"
        print(configuration)

        best_loss = float('inf')
        patience_counter = 0
        best_model_weights = copy.deepcopy(model.state_dict())

        log_data = []

        for epoch in range(num_epochs):
            model.train()
            sum_loss_pitch_gaze = 0
            sum_loss_yaw_gaze = 0
            iter_gaze = 0

            for i, (images_gaze, labels_gaze, cont_labels_gaze, name) in enumerate(train_loader_gaze):
                pitch, yaw = model(images_gaze.to(device))

                loss_pitch_gaze = criterion(pitch, labels_gaze[:, 0].to(device))
                loss_yaw_gaze = criterion(yaw, labels_gaze[:, 1].to(device))

                pitch_predicted = softmax(pitch)
                yaw_predicted = softmax(yaw)

                pitch_predicted = torch.sum(pitch_predicted * idx_tensor, 1) * 3 - 42
                yaw_predicted = torch.sum(yaw_predicted * idx_tensor, 1) * 3 - 42

                loss_reg_pitch = reg_criterion(pitch_predicted, cont_labels_gaze[:, 0].to(device))
                loss_reg_yaw = reg_criterion(yaw_predicted, cont_labels_gaze[:, 1].to(device))

                loss_pitch_gaze += alpha * loss_reg_pitch
                loss_yaw_gaze += alpha * loss_reg_yaw

                sum_loss_pitch_gaze += loss_pitch_gaze.item()
                sum_loss_yaw_gaze += loss_yaw_gaze.item()

                total_loss = loss_pitch_gaze + loss_yaw_gaze
                optimizer_gaze.zero_grad(set_to_none=True)
                total_loss.backward()
                optimizer_gaze.step()

                iter_gaze += 1

                if (i + 1) % 100 == 0:
                    current_lr = optimizer_gaze.param_groups[0]['lr']
                    print(
                        "Epoch [%d/%d], Iter [%d/%d] Losses: "
                        "Gaze Yaw %.4f, Gaze Pitch %.4f, learning_rate: %.6f"
                        % (
                            epoch + 1,
                            num_epochs,
                            i + 1,
                            len(train_dataset) // batch_size,
                            sum_loss_yaw_gaze / iter_gaze,
                            sum_loss_pitch_gaze / iter_gaze,
                            current_lr
                        )
                    )

            scheduler_gaze.step()

            # Validation
            model.eval()
            val_loss_pitch_gaze = 0
            val_loss_yaw_gaze = 0
            val_iter_gaze = 0
            total_angle_error = 0

            with torch.no_grad():
                for images_gaze, labels_gaze, cont_labels_gaze, name in val_loader_gaze:
                    pitch, yaw = model(images_gaze.to(device))

                    loss_pitch_gaze = criterion(pitch, labels_gaze[:, 0].to(device))
                    loss_yaw_gaze = criterion(yaw, labels_gaze[:, 1].to(device))

                    pitch_predicted = softmax(pitch)
                    yaw_predicted = softmax(yaw)

                    pitch_predicted = torch.sum(pitch_predicted * idx_tensor, 1) * 3 - 42
                    yaw_predicted = torch.sum(yaw_predicted * idx_tensor, 1) * 3 - 42

                    loss_reg_pitch = reg_criterion(pitch_predicted, cont_labels_gaze[:, 0].to(device))
                    loss_reg_yaw = reg_criterion(yaw_predicted, cont_labels_gaze[:, 1].to(device))

                    loss_pitch_gaze += alpha * loss_reg_pitch
                    loss_yaw_gaze += alpha * loss_reg_yaw

                    angle_error = compute_angular_error(
                        torch.stack((pitch_predicted, yaw_predicted), dim=1),
                        cont_labels_gaze.to(device)
                    )
                    total_angle_error += angle_error.item()

                    val_loss_pitch_gaze += loss_pitch_gaze.item()
                    val_loss_yaw_gaze += loss_yaw_gaze.item()
                    val_iter_gaze += 1

            avg_val_loss_pitch_gaze = val_loss_pitch_gaze / val_iter_gaze
            avg_val_loss_yaw_gaze = val_loss_yaw_gaze / val_iter_gaze
            avg_val_loss = (avg_val_loss_pitch_gaze + avg_val_loss_yaw_gaze) / 2
            mean_angular_error = total_angle_error / val_iter_gaze

            print(
                "Validation - Epoch [%d/%d] Losses: "
                "Gaze Yaw %.4f, Gaze Pitch %.4f, Average loss: %.4f, Mean Angular Error: %.4f"
                % (
                    epoch + 1,
                    num_epochs,
                    avg_val_loss_yaw_gaze,
                    avg_val_loss_pitch_gaze,
                    avg_val_loss,
                    mean_angular_error
                )
            )

            log_data.append({
                'epoch': epoch + 1,
                'train_loss_pitch_gaze': sum_loss_pitch_gaze / iter_gaze,
                'train_loss_yaw_gaze': sum_loss_yaw_gaze / iter_gaze,
                'val_loss_pitch_gaze': avg_val_loss_pitch_gaze,
                'val_loss_yaw_gaze': avg_val_loss_yaw_gaze,
                'val_loss_avg': avg_val_loss,
                'mean_angular_error': mean_angular_error,
                'learning_rate': optimizer_gaze.param_groups[0]['lr']
            })

            writer.add_scalars('Loss', {
                'Train/Pitch': sum_loss_pitch_gaze / iter_gaze,
                'Train/Yaw': sum_loss_yaw_gaze / iter_gaze,
                'Validation/Pitch': avg_val_loss_pitch_gaze,
                'Validation/Yaw': avg_val_loss_yaw_gaze
            }, epoch + 1)
            writer.add_scalar('Learning Rate', optimizer_gaze.param_groups[0]['lr'], epoch + 1)
            writer.add_scalar('Mean Angular Error', mean_angular_error, epoch + 1)

            if avg_val_loss < best_loss:
                best_loss = avg_val_loss
                patience_counter = 0
                best_model_weights = copy.deepcopy(model.state_dict())
                torch.save(best_model_weights, os.path.join(summary_path, "best_model.pt"))
            else:
                patience_counter += 1

            if patience_counter >= patience:
                print(f"Early stopping at epoch {epoch + 1}")
                break

        torch.save(model.state_dict(), os.path.join(summary_path, "last_model.pt"))

        log_df = pd.DataFrame(log_data)
        log_df.to_csv(os.path.join(summary_path, 'training_log.csv'), index=False)

        config = vars(args)
        config.update(model_params)
        with open(os.path.join(summary_path, 'config.json'), 'w') as f:
            json.dump(config, f, indent=4)

    writer.close()
