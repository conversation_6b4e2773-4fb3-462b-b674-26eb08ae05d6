import os
import torch
import numpy as np
import torch.nn as nn
import onnxruntime as ort
from torchvision import transforms
from torch.utils.data import DataLoader
import argparse

from l2cs import Gaze360, gazeto3d, angular

def parse_args():
    parser = argparse.ArgumentParser(description="Model Validation with ONNX")
    parser.add_argument("--gaze360image_dir", type=str, default="preprocessing/gaze360/Image")
    parser.add_argument("--gaze360label_dir", type=str, default="preprocessing/gaze360/Label/test.label")
    parser.add_argument("--model_path", type=str, default="output_gaze360_new_strategy_pha/onnx_models/mobilenetv4_448_grayscale.onnx")
    parser.add_argument("--batch_size", type=int, default=16)
    parser.add_argument("--bin_width", type=int, default=4)
    parser.add_argument("--gpu_id", type=str, default="0")
    return parser.parse_args()

def load_onnx_model(model_path):
    return ort.InferenceSession(model_path)

def select_device(gpu_id):
    # Checks if GPU is available and if a specific GPU ID is given
    if torch.cuda.is_available() and gpu_id != 'cpu':
        device = torch.device(f"cuda:{gpu_id}")
        print(f"Using GPU: {torch.cuda.get_device_name(device)}")
    else:
        device = torch.device("cpu")
        print("Using CPU")
    return device


def validate_onnx_model(session, dataloader, bin_width, device):
    total_angle_error = 0.0
    total_samples = 0

    for images, labels, cont_labels, _ in dataloader:
        # Prepare input to ONNX Runtime: needs to be a NumPy array
        images_np = images.numpy()
        ort_inputs = {session.get_inputs()[0].name: images_np}
        ort_outs = session.run(None, ort_inputs)

        pitch = torch.from_numpy(ort_outs[0]).to(device)  # Move output to the correct device
        yaw = torch.from_numpy(ort_outs[1]).to(device)    # Move output to the correct device

        softmax = nn.Softmax(dim=1).to(device)
        idx_tensor = torch.FloatTensor([idx for idx in range(90)]).to(device)

        pitch_predicted = softmax(pitch)
        yaw_predicted = softmax(yaw)
        
        pitch_predicted = (
            torch.sum(pitch_predicted * idx_tensor, 1) * bin_width - 180
        )
        yaw_predicted = (
            torch.sum(yaw_predicted * idx_tensor, 1) * bin_width - 180
        )
        
        # converting to the radian.
        pitch_predicted_rad = pitch_predicted * np.pi / 180
        yaw_predicted_rad = yaw_predicted * np.pi / 180
        
        label_pitch = cont_labels[:, 0].float() * np.pi / 180
        label_yaw = cont_labels[:, 1].float() * np.pi / 180

        pitch_predicted_np = pitch_predicted_rad.detach().cpu().numpy()
        yaw_predicted_np = yaw_predicted_rad.detach().cpu().numpy()

        for p, y, pl, yl in zip(pitch_predicted_np, yaw_predicted_np, label_pitch, label_yaw):
            total_angle_error += angular(
                gazeto3d([p.item(), y.item()]), gazeto3d([pl.item(), yl.item()])
            )

        total_samples += cont_labels.size(0)

    mean_angular_error = total_angle_error / total_samples
    print(f"Validation completed: Mean Angular Error: {mean_angular_error:.4f}")


if __name__ == "__main__":
    args = parse_args()
    device = select_device(args.gpu_id)  # This should set the device correctly for CUDA or CPU

    transformations = transforms.Compose([
        transforms.Resize(448),
        transforms.Grayscale(num_output_channels=3),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
    ])

    val_dataset = Gaze360(args.gaze360label_dir, args.gaze360image_dir, transformations, 180, 4, train=False)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=4, pin_memory=True)

    onnx_session = load_onnx_model(args.model_path)
    validate_onnx_model(onnx_session, val_loader, args.bin_width, device)




'''

python test_gaze_onnx.py --model_path output_gaze360_new_strategy_pha/models_for_ti_board/PHA_GazeEstimation_dev_mobilenetv4_IR_V1.onnx
    
'''