
# from l2cs import L2CS, select_device, Gaze360, <PERSON><PERSON><PERSON><PERSON>, gazeto3d, angular, MOBILEV4_ADA


# my_model = MOBILEV4_ADA(num_bins= 90)



# for name, param in my_model.named_parameters():
#     print(f" name: {name}, shape: {param.data.shape}")

import pandas as pd


df = pd.read_csv("/hdd/lg_display_project/L2CS-Net/output_gaze360/snapshots/L2CS-gaze360-mobilenetv4-ada_1723447598/best_model/training_log.csv", sep=" ", header=None)
print(df)