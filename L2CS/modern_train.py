import os 
import argparse 
import time 
import torch 
import torch.nn as nn 
from torch.utils.data import DataLoader, Dataset 
from torchvision import transforms 
import torch.backends.cudnn as cudnn 
from accelerate import Accelerator
from torch.optim.lr_scheduler import StepLR 
from l2cs import L2CS_ViT, Mpiigaze 

def parse_args():
    parser = argparse.ArgumentParser(description="Gaze estimation using L2CSNet") 

    parser.add_argument(
        "--gazeMpiimage_dir", 
        dest="gazeMpiimage_dir", 
        help="Directory path for gaze images", 
        default="datasets/MPIIFaceGaze/Image", 
        type=str,
    )

    parser.add_argument(
        "--gazeMpiilabel_dir", 
        dest="gazeMpiilabel_dir", 
        help="Directory path for gaze labels", 
        default="datasets/MPIIFaceGaze/Label",
        type=str,
    )

    parser.add_argument(
        "--dataset", 
        dest="dataset", 
        help="mpiigaze, rtgene, gaze360, ethgaze",
        default="mpiigaze", 
        type=str,
    )

    parser.add_argument(
        "--output", 
        dest="output", 
        help="Path of output models",
        default="output/snapshots/", 
        type=str,
    )
    
    parser.add_argument(
        "--gpu", 
        dest="gpu_id", 
        help="GPU device id to use [0] or multiple 0,1,2,3", 
        default="0", 
        type=str,
    )

    parser.add_argument(
        "--num_epochs", 
        dest="num_epochs", 
        help="Maximum number of training epochs", 
        default=1, 
        type=int,
    )

    parser.add_argument(
        "--batch_size", 
        dest="batch_size", 
        help="Batch Size", 
        default=1, 
        type=int
    )

    parser.add_argument(
        "--arch", 
        dest="arch", 
        help="Network architecture, can be: L2CS_ViT, ResNet18, ResNet34, ...", 
        default="L2CS_ViT", 
        type=str,
    )

    parser.add_argument(
        "--alpha", 
        dest="alpha", 
        help="Regression loss coefficient", 
        default=1, 
        type=float, 
    )

    parser.add_argument(
        "--lr", 
        dest="lr", 
        help="Base learning rate", 
        default=0.00001, 
        type=float
    )

    parser.add_argument(
        "--patience", 
        dest="patience", 
        help="Patience for early stopping", 
        default=10, 
        type=int
    )

    args = parser.parse_args()
    return args 

def get_model(arch, bins):
    assert arch == "L2CS_ViT"
    image_size = 448 
    patch_size = 16 
    num_classes = bins 
    dim = 512 
    depth = 6 
    heads = 8 
    mlp_dim = 1024 
    model = L2CS_ViT(
        image_size, patch_size, num_classes, dim, depth, heads, mlp_dim
    )
    return model 

if __name__ == "__main__":
    args = parse_args() 
    cudnn.enabled = True 
    num_epochs = args.num_epochs 
    batch_size = args.batch_size 
    data_set = args.dataset 
    alpha = args.alpha 
    output = args.output 
    patience = args.patience

    transformations = transforms.Compose(
        [
            transforms.Resize(448), 
            transforms.ToTensor(), 
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]), 
        ]
    )

    assert data_set == "mpiigaze"
    folder = os.listdir(args.gazeMpiilabel_dir) 
    folder.sort()
    testlabelpathombined = [os.path.join(args.gazeMpiilabel_dir, j) for j in folder] 

    
    for fold in range(15):
        accelerator = Accelerator()
        model = get_model(args.arch, 28) 
        train_dataset = Mpiigaze(testlabelpathombined, args.gazeMpiimage_dir, transformations, True, fold = fold)
        val_dataset = Mpiigaze(testlabelpathombined, args.gazeMpiimage_dir, transformations, False, fold = fold)
        
        train_loader_gaze = DataLoader(
            dataset=train_dataset, 
            batch_size=int(batch_size),
            shuffle=True, 
            num_workers=4, 
            pin_memory=True
        )

        val_loader_gaze = DataLoader(
            dataset=val_dataset, 
            batch_size=int(batch_size),
            shuffle=False, 
            num_workers=4, 
            pin_memory=True
        )

        optimizer_gaze = torch.optim.AdamW(
            model.parameters(), 
            lr=args.lr, 
            weight_decay=1e-2, 
            betas=(0.9, 0.999), 
            eps=1e-8
        )

        scheduler_gaze = StepLR(optimizer_gaze, step_size=10, gamma=0.1)

        model, train_loader_gaze, val_loader_gaze, optimizer_gaze, scheduler_gaze = accelerator.prepare(
            model, train_loader_gaze, val_loader_gaze, optimizer_gaze, scheduler_gaze
        )

        summary_name = "{}_{}".format("L2CS-mpiigaze", int(time.time()))
        summary_path = os.path.join(output, "{}".format(summary_name), "fold" + str(fold))
        os.makedirs(summary_path, exist_ok=True)

        criterion = nn.CrossEntropyLoss()
        reg_criterion = nn.MSELoss() 
        softmax = nn.Softmax(dim=1) 
        idx_tensor = torch.FloatTensor([idx for idx in range(28)]).to(accelerator.device)

        configuration = f"\ntrain configuration, gpu_id={args.gpu_id}, batch_size={batch_size}, model_arch={args.arch}\n Start training dataset={data_set}, loader={len(train_loader_gaze)}, fold={fold}--------------\n"
        print(configuration) 

        best_loss = float('inf')
        patience_counter = 0

        for epoch in range(num_epochs):
            model.train()
            sum_loss_pitch_gaze = 0
            sum_loss_yaw_gaze = 0
            iter_gaze = 0

            for i, (images_gaze, labels_gaze, cont_labels_gaze, name) in enumerate(train_loader_gaze):
                pitch, yaw = model(images_gaze) 

                loss_pitch_gaze = criterion(pitch, labels_gaze[:, 0])
                loss_yaw_gaze = criterion(yaw, labels_gaze[:, 1]) 

                pitch_predicted = softmax(pitch) 
                yaw_predicted = softmax(yaw) 

                pitch_predicted = torch.sum(pitch_predicted * idx_tensor, 1) * 3 - 42 
                yaw_predicted = torch.sum(yaw_predicted * idx_tensor, 1) * 3 - 42 

                loss_reg_pitch = reg_criterion(pitch_predicted, cont_labels_gaze[:, 0])
                loss_reg_yaw = reg_criterion(yaw_predicted, cont_labels_gaze[:, 1]) 

                loss_pitch_gaze += alpha * loss_reg_pitch 
                loss_yaw_gaze += alpha * loss_reg_yaw  

                #loss_seq = [loss_pitch_gaze, loss_yaw_gaze] 
                #grad_seq = [torch.tensor(1.0, device=accelerator.device) for _ in range(len(loss_seq))]

                total_loss = loss_pitch_gaze + loss_yaw_gaze
                optimizer_gaze.zero_grad(set_to_none=True)
                # accelerator.backward(loss_pitch_gaze, retain_graph = True)
                # accelerator.backward(loss_yaw_gaze)
                accelerator.backward(total_loss)
                optimizer_gaze.step()
                scheduler_gaze.step()

                sum_loss_pitch_gaze += loss_pitch_gaze.item()
                sum_loss_yaw_gaze += loss_yaw_gaze.item()
                iter_gaze += 1 

                if (i + 1) % 100 == 0:
                    print(
                        "Epoch [%d/%d], Iter [%d/%d] Losses: "
                        "Gaze Yaw %.4f, Gaze Pitch %.4f"
                        % (
                            epoch + 1, 
                            num_epochs, 
                            i + 1, 
                            len(train_loader_gaze), 
                            sum_loss_yaw_gaze / iter_gaze, 
                            sum_loss_pitch_gaze / iter_gaze,
                        )
                    )

            # scheduler_gaze.step()

            # Validation
            model.eval()
            val_loss_pitch_gaze = 0
            val_loss_yaw_gaze = 0
            val_iter_gaze = 0

            with torch.no_grad():
                for images_gaze, labels_gaze, cont_labels_gaze, name in val_loader_gaze:
                    pitch, yaw = model(images_gaze)

                    loss_pitch_gaze = criterion(pitch, labels_gaze[:, 0])
                    loss_yaw_gaze = criterion(yaw, labels_gaze[:, 1])

                    pitch_predicted = softmax(pitch)
                    yaw_predicted = softmax(yaw)

                    pitch_predicted = torch.sum(pitch_predicted * idx_tensor, 1) * 3 - 42
                    yaw_predicted = torch.sum(yaw_predicted * idx_tensor, 1) * 3 - 42

                    loss_reg_pitch = reg_criterion(pitch_predicted, cont_labels_gaze[:, 0])
                    loss_reg_yaw = reg_criterion(yaw_predicted, cont_labels_gaze[:, 1])

                    loss_pitch_gaze += alpha * loss_reg_pitch
                    loss_yaw_gaze += alpha * loss_reg_yaw

                    val_loss_pitch_gaze += loss_pitch_gaze.item()
                    val_loss_yaw_gaze += loss_yaw_gaze.item()
                    val_iter_gaze += 1

            avg_val_loss_pitch_gaze = val_loss_pitch_gaze / val_iter_gaze
            avg_val_loss_yaw_gaze = val_loss_yaw_gaze / val_iter_gaze
            avg_val_loss = (avg_val_loss_pitch_gaze + avg_val_loss_yaw_gaze) / 2

            print(
                "Validation - Epoch [%d/%d] Losses: "
                "Gaze Yaw %.4f, Gaze Pitch %.4f"
                % (
                    epoch + 1, 
                    num_epochs, 
                    avg_val_loss_yaw_gaze, 
                    avg_val_loss_pitch_gaze,
                )
            )

            if avg_val_loss < best_loss:
                best_loss = avg_val_loss
                patience_counter = 0
                torch.save(model.state_dict(), os.path.join(summary_path, "best_model.pt"))
            else:
                patience_counter += 1

            if patience_counter >= patience:
                print(f"Early stopping at epoch {epoch+1}")
                break

        # Save the last model weights
        torch.save(model.state_dict(), os.path.join(summary_path, "last_model.pt"))
