import argparse
import pathlib
import torch
import torch.backends.cudnn as cudnn

from l2cs import select_device, draw_gaze, getArch, Pipeline, render

CWD = pathlib.Path.cwd()


def parse_args():
    """Parse input arguments."""
    parser = argparse.ArgumentParser(
        description='Gaze evalution using model pretrained with L2CS-Net on Gaze360.')
    parser.add_argument(
        '--device',dest='device', help='Device to run model: cpu or gpu:0',
        default="gpu", type=str)
    parser.add_argument(
        '--snapshot',dest='snapshot', help='Path of model snapshot.', 
        default='/home/<USER>/output/eViT_v02/L2CS-mpiigaze_1721364309/fold0/best_model.pt', type=str)
    parser.add_argument(
        '--arch', dest='arch',help='Network architecture, can be: ResNet18, ResNet34, ResNet50, ResNet101, ResNet152',
        default='', type=str)
    parser.add_argument(
        '--bins',dest='bins', help='Number of bins in L2CS-Net.', 
        default=90, type=int)
    parser.add_argument(
        '--onnx',dest='onnx_model_path', help='Path of onnx model.', 
        default='/home/<USER>/output/eViT_v02/L2CS-mpiigaze_1721364309/fold0/best_model.onnx', type=str)
    
    
    args = parser.parse_args()
    return args


if __name__ == '__main__':
    args = parse_args()

    cudnn.enabled = True
    arch=args.arch

    if args.arch == "L2CS_EfficientViT":
        model = getArch(arch, args.bins)
    elif args.arch == "MobileNetV4_Ada":
        model = getArch(arch, args.bins)
    elif args.arch == "MobileNetV4":
        model = getArch(arch, args.bins)
    else:
        model = getArch(arch, 90)
        
    
    model.load_state_dict(torch.load(args.snapshot, map_location=args.device))
    model.eval()
    
    # Create a dummy input tensor with the correct shape
    dummy_input = torch.randn(16, 3, 448, 448).to(args.device)
    
    # Export the model to ONNX
    torch.onnx.export(model, 
                      dummy_input, 
                      args.onnx_model_path,
                      export_params=True, 
                      opset_version=12,  # Using a newer opset version for better compatibility
                      do_constant_folding=True,
                      input_names=['input'], 
                      output_names=['output'],
                      dynamic_axes={'input' : {0 : 'batch_size'},  # Dynamically manage batch size
                                    'output' : {0 : 'batch_size'}})


'''
Running command

python pt_to_onnx.py --device cpu --snapshot output_gaze360_new_strategy_pha/snapshots/L2CS-gaze360-mobilenetv4-ada_1729665978/best_model/last_model.pt \
    --onnx output_gaze360_new_strategy_pha/onnx_models/mobilenetv4_ada_448_grayscale.onnx \
    --arch MobileNetV4_Ada

'''