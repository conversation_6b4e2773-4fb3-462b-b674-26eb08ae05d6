import os
import argparse
import time
import numpy as np 
import torch.utils.model_zoo as model_zoo
import torch
import torch.nn as nn
from torch.autograd import Variable
from torch.utils.data import DataLoader
from torchvision import transforms
import torch.backends.cudnn as cudnn
import torchvision
import math
from l2cs import L2CS, select_device, Gaze360, Mpiigaze, gazeto3d, angular, L2CS_ADA

def parse_args():
    """Parse input arguments."""
    parser = argparse.ArgumentParser(description='Gaze estimation using L2CSNet.')
    parser.add_argument('--gaze360image_dir', dest='gaze360image_dir', help='Directory path for gaze images.', default='datasets/Gaze360/Image', type=str)
    parser.add_argument('--gaze360label_dir', dest='gaze360label_dir', help='Directory path for gaze labels.', default='datasets/Gaze360/Label/train.label', type=str)
    parser.add_argument('--gazeMpiimage_dir', dest='gazeMpiimage_dir', help='Directory path for gaze images.', default='datasets/MPIIFaceGaze/Image', type=str)
    parser.add_argument('--gazeMpiilabel_dir', dest='gazeMpiilabel_dir', help='Directory path for gaze labels.', default='datasets/MPIIFaceGaze/Label', type=str)
    parser.add_argument('--dataset', dest='dataset', help='mpiigaze, rtgene, gaze360, ethgaze', default="mpiigaze", type=str)
    parser.add_argument('--output', dest='output', help='Path of output models.', default='outputADA/snapshots', type=str)
    parser.add_argument('--snapshot', dest='snapshot', help='Path of model snapshot.', default='', type=str)
    parser.add_argument('--gpu', dest='gpu_id', help='GPU device id to use [0] or multiple 0,1,2,3', default='0', type=str)
    parser.add_argument('--num_epochs', dest='num_epochs', help='Maximum number of training epochs.', default=60, type=int)
    parser.add_argument('--batch_size', dest='batch_size', help='Batch size.', default=32, type=int)
    parser.add_argument('--arch', dest='arch', help='Network architecture, can be: ResNet18, ResNet34, [ResNet50], ResNet101, ResNet152, Squeezenet_1_0, Squeezenet_1_1, MobileNetV2', default='ResNet50', type=str)
    parser.add_argument('--alpha', dest='alpha', help='Regression loss coefficient.', default=1, type=float)
    parser.add_argument('--lr', dest='lr', help='Base learning rate.', default=0.00001, type=float)
    parser.add_argument("--bin-width", dest="bin_width", help="Gaze digitize bin width, check dataset for more info", default=3, type=int)
    parser.add_argument("--angle", dest="angle", help="This is the angle threshold for pitch/yaw, like 42 in our case.", default=42, type=int)
    args = parser.parse_args()
    return args

def get_ignored_params(model):
    # b = [model.conv1, model.bn1, model.fc_finetune]
    b = [model.conv1, model.bn1]
    for i in range(len(b)):
        for module_name, module in b[i].named_modules():
            if 'bn' in module_name:
                module.eval()
            for name, param in module.named_parameters():
                yield param

def get_non_ignored_params(model):
    b = [model.layer1, model.layer2, model.layer3, model.layer4]
    for i in range(len(b)):
        for module_name, module in b[i].named_modules():
            if 'bn' in module_name:
                module.eval()
            for name, param in module.named_parameters():
                yield param

def get_fc_params(model):
    b = [model.fc_yaw_gaze, model.fc_pitch_gaze]
    for i in range(len(b)):
        for module_name, module in b[i].named_modules():
            for name, param in module.named_parameters():
                yield param

def load_filtered_state_dict(model, snapshot):
    model_dict = model.state_dict()
    snapshot = {k: v for k, v in snapshot.items() if k in model_dict}
    model_dict.update(snapshot)
    model.load_state_dict(model_dict)

def getArch_weights(arch, bins):
    if arch == 'ResNet18':
        model = L2CS(torchvision.models.resnet.BasicBlock, [2, 2, 2, 2], bins)
        pre_url = 'https://download.pytorch.org/models/resnet18-5c106cde.pth'
    elif arch == 'ResNet34':
        model = L2CS(torchvision.models.resnet.BasicBlock, [3, 4, 6, 3], bins)
        pre_url = 'https://download.pytorch.org/models/resnet34-333f7ec4.pth'
    elif arch == 'ResNet101':
        model = L2CS(torchvision.models.resnet.Bottleneck, [3, 4, 23, 3], bins)
        pre_url = 'https://download.pytorch.org/models/resnet101-5d3b4d8f.pth'
    elif arch == 'ResNet152':
        model = L2CS(torchvision.models.resnet.Bottleneck,[3, 8, 36, 3], bins)
        pre_url = 'https://download.pytorch.org/models/resnet152-b121ed2d.pth'
    else:
        if arch != 'ResNet50':
            print('Invalid value for architecture is passed! The default value of ResNet50 will be used instead!')
        model = L2CS_ADA(torchvision.models.resnet.Bottleneck, [3, 4, 6, 3], bins)
        pre_url = 'https://download.pytorch.org/models/resnet50-19c8e357.pth'
    return model, pre_url

def spherical2cartesian(x):
    output = torch.zeros(x.size(0), 3)
    output[:, 2] = -torch.cos(x[:, 1]) * torch.cos(x[:, 0])
    output[:, 0] = torch.cos(x[:, 1]) * torch.sin(x[:, 0])
    output[:, 1] = torch.sin(x[:, 1])
    return output

def compute_angular_error(input, target):
    input = spherical2cartesian(input)
    target = spherical2cartesian(target)
    input = input.view(-1, 3, 1)
    target = target.view(-1, 1, 3)
    output_dot = torch.bmm(target, input)
    output_dot = output_dot.view(-1)
    output_dot = torch.acos(output_dot)
    output_dot = output_dot.data
    output_dot = 180 * torch.mean(output_dot) / math.pi
    return output_dot

if __name__ == "__main__":

    args = parse_args()
    cudnn.enabled = True
    num_epochs = args.num_epochs
    batch_size = args.batch_size
    gpu = "cuda:" + args.gpu_id
    device = gpu 
    data_set = args.dataset
    alpha = args.alpha
    output = args.output
    angle = args.angle 
    bin_width = args.bin_width

    transformations = transforms.Compose([
        transforms.Resize(448),
        transforms.ToTensor(),
        transforms.Normalize(
            mean=[0.485, 0.456, 0.406],
            std=[0.229, 0.224, 0.225]
        )
    ])

    assert data_set == "mpiigaze", f"Our code is currently able to handle MPIIGAZE only."

    folder = os.listdir(args.gazeMpiilabel_dir)
    folder.sort()
    testlabelpathombined = [os.path.join(args.gazeMpiilabel_dir, j) for j in folder]
    for fold in range(15):
        num_classes = len(range(-1 * angle, angle, bin_width))
        model, pre_url = getArch_weights(args.arch, num_classes)
        load_filtered_state_dict(model, model_zoo.load_url(pre_url))
        model.to(gpu)
        print('Loading data.')
        train_dataset = Mpiigaze(testlabelpathombined, args.gazeMpiimage_dir, transformations, train=True, angle=angle, bin_width=bin_width, fold=fold)
        val_dataset = Mpiigaze(testlabelpathombined, args.gazeMpiimage_dir, transformations, train=False, angle=angle, bin_width=bin_width, fold=fold)

        train_loader_gaze = DataLoader(
            dataset=train_dataset,
            batch_size=int(batch_size),
            shuffle=True,
            num_workers=16,
            pin_memory=True
        )

        val_loader_gaze = DataLoader(
            dataset=val_dataset,
            batch_size=int(batch_size),
            shuffle=False,
            num_workers=16,
            pin_memory=True
        )

        torch.backends.cudnn.benchmark = True

        summary_name = "{}_{}".format("L2CS-mpiigaze", int(time.time()))
        summary_path = os.path.join(output, "{}".format(summary_name), "fold" + str(fold))
        os.makedirs(summary_path, exist_ok=True)
        
        criterion = nn.CrossEntropyLoss().cuda(gpu)
        reg_criterion = nn.MSELoss().cuda(gpu)
        softmax = nn.Softmax(dim=1).cuda(gpu)
        idx_tensor = [idx for idx in range(num_classes)]
        idx_tensor = Variable(torch.FloatTensor(idx_tensor)).cuda(gpu)

        # Optimizer gaze
        optimizer_gaze = torch.optim.Adam([
            {'params': get_ignored_params(model), 'lr': 0},
            {'params': get_non_ignored_params(model), 'lr': args.lr},
            {'params': get_fc_params(model), 'lr': args.lr}
        ], args.lr)

        configuration = f"\ntrain configuration, gpu_id={args.gpu_id}, batch_size={batch_size}, model_arch={args.arch}\n Start training dataset={data_set}, loader={len(train_loader_gaze)}, fold={fold}--------------\n"
        print(configuration)

        for epoch in range(num_epochs):
            model.train()
            sum_loss_pitch_gaze = sum_loss_yaw_gaze = iter_gaze = 0

            # Calculate the weight for AdaFace loss and normal loss
            aface_weight = max(0, 1 - epoch / num_epochs)
            normal_weight = min(1, epoch / num_epochs)
            
            for i, (images_gaze, labels_gaze, cont_labels_gaze, name) in enumerate(train_loader_gaze):
                images_gaze = Variable(images_gaze).cuda(gpu)

                # Binned labels
                label_pitch_gaze = Variable(labels_gaze[:, 0]).cuda(gpu)
                label_yaw_gaze = Variable(labels_gaze[:, 1]).cuda(gpu)

                # Continuous labels
                label_pitch_cont_gaze = Variable(cont_labels_gaze[:, 0]).cuda(gpu)
                label_yaw_cont_gaze = Variable(cont_labels_gaze[:, 1]).cuda(gpu)

                ada_pitch, ada_yaw, pitch, yaw = model(images_gaze, labels_gaze.cuda(gpu))



                # Normal cross-entropy loss
                loss_pitch_gaze = criterion(pitch, label_pitch_gaze)
                loss_yaw_gaze = criterion(yaw, label_yaw_gaze)

                # MSE loss for regression
                pitch_predicted = softmax(pitch)
                yaw_predicted = softmax(yaw)

                pitch_predicted = torch.sum(pitch_predicted * idx_tensor, 1) * bin_width - angle
                yaw_predicted = torch.sum(yaw_predicted * idx_tensor, 1) * bin_width - angle

                loss_reg_pitch = reg_criterion(pitch_predicted, label_pitch_cont_gaze)
                loss_reg_yaw = reg_criterion(yaw_predicted, label_yaw_cont_gaze)

                loss_pitch_gaze += alpha * loss_reg_pitch
                loss_yaw_gaze += alpha * loss_reg_yaw

                # AdaFace loss
                ##################################################################

                ada_loss_pitch_gaze = criterion(ada_pitch, label_pitch_gaze)
                ada_loss_yaw_gaze = criterion(ada_yaw, label_yaw_gaze)

                # MSE loss for regression
                ada_pitch_predicted = softmax(ada_pitch)
                ada_yaw_predicted = softmax(ada_yaw)

                ada_pitch_predicted = torch.sum(ada_pitch_predicted * idx_tensor, 1) * bin_width - angle
                ada_yaw_predicted = torch.sum(ada_yaw_predicted * idx_tensor, 1) * bin_width - angle

                ada_loss_reg_pitch = reg_criterion(ada_pitch_predicted, label_pitch_cont_gaze)
                ada_loss_reg_yaw = reg_criterion(ada_yaw_predicted, label_yaw_cont_gaze)

                ada_loss_pitch_gaze += alpha * ada_loss_reg_pitch
                ada_loss_yaw_gaze += alpha * ada_loss_reg_yaw



                # Combined loss with weighted AdaFace and normal loss
                loss_pitch = aface_weight * ada_loss_pitch_gaze + normal_weight * loss_pitch_gaze
                loss_yaw = aface_weight * ada_loss_yaw_gaze + normal_weight * loss_yaw_gaze

                sum_loss_pitch_gaze += loss_pitch
                sum_loss_yaw_gaze += loss_yaw

                loss_seq = [loss_pitch, loss_yaw]
                grad_seq = [torch.tensor(1.0).cuda(gpu) for _ in range(len(loss_seq))]

                optimizer_gaze.zero_grad(set_to_none=True)
                torch.autograd.backward(loss_seq, grad_seq)
                optimizer_gaze.step()

                iter_gaze += 1

                if (i+1) % 100 == 0:
                    print('Epoch [%d/%d], Iter [%d/%d] Losses: '
                        'Gaze Yaw %.4f,Gaze Pitch %.4f' % (
                            epoch+1,
                            num_epochs,
                            i+1,
                            len(train_dataset)//batch_size,
                            sum_loss_yaw_gaze/iter_gaze,
                            sum_loss_pitch_gaze/iter_gaze
                        )
                        )

            # Save models at numbered epochs.
            if epoch % 1 == 0 and epoch < num_epochs:
                print('Taking snapshot...',
                    torch.save(model.state_dict(),
                                os.path.join(summary_path,
                                '_epoch_' + str(epoch+1) + '.pkl')
                    ))

            # Validation
            model.eval()
            val_loss_pitch_gaze = 0
            val_loss_yaw_gaze = 0
            val_iter_gaze = 0
            total_angle_error = 0.0
            total_samples = 0

            with torch.no_grad():
                for images_gaze, labels_gaze, cont_labels_gaze, name in val_loader_gaze:
                    pitch, yaw = model(images_gaze.to(device), training = False)

                    total_samples += cont_labels_gaze.size(0)
                    loss_pitch_gaze = criterion(pitch, labels_gaze[:, 0].to(device))
                    loss_yaw_gaze = criterion(yaw, labels_gaze[:, 1].to(device))

                    pitch_predicted = softmax(pitch)
                    yaw_predicted = softmax(yaw)

                    pitch_predicted = torch.sum(pitch_predicted * idx_tensor, 1) * bin_width - angle
                    yaw_predicted = torch.sum(yaw_predicted * idx_tensor, 1) * bin_width - angle

                    label_pitch = cont_labels_gaze[:, 0].float() * np.pi / 180 
                    label_yaw = cont_labels_gaze[:, 1].float() * np.pi / 180 

                    pitch_predicted_rad = pitch_predicted * np.pi / 180 
                    yaw_predicted_rad = yaw_predicted * np.pi / 180 

                    loss_reg_pitch = reg_criterion(pitch_predicted, cont_labels_gaze[:, 0].to(device))
                    loss_reg_yaw = reg_criterion(yaw_predicted, cont_labels_gaze[:, 1].to(device))

                    loss_pitch_gaze += alpha * loss_reg_pitch
                    loss_yaw_gaze += alpha * loss_reg_yaw

                    pitch_predicted_np = pitch_predicted_rad.detach().cpu().numpy()
                    yaw_predicted_np = yaw_predicted_rad.detach().cpu().numpy()
                    for p, y, pl, yl in zip(pitch_predicted_np, yaw_predicted_np, label_pitch, label_yaw):
                        total_angle_error += angular(gazeto3d([p, y]), gazeto3d([pl, yl]))
                    
                    val_loss_pitch_gaze += loss_pitch_gaze.item()
                    val_loss_yaw_gaze += loss_yaw_gaze.item()
                    val_iter_gaze += 1

            avg_val_loss_pitch_gaze = val_loss_pitch_gaze / val_iter_gaze
            avg_val_loss_yaw_gaze = val_loss_yaw_gaze / val_iter_gaze
            avg_val_loss = (avg_val_loss_pitch_gaze + avg_val_loss_yaw_gaze) / 2
            mean_angular_error = total_angle_error / total_samples
            
            print(
                "Validation - Epoch [%d/%d] Losses: "
                "Gaze Yaw %.4f, Gaze Pitch %.4f, Average loss: %.4f, Mean Angular Error: %.4f"
                % (
                    epoch + 1,
                    num_epochs,
                    avg_val_loss_yaw_gaze,
                    avg_val_loss_pitch_gaze,
                    avg_val_loss,
                    mean_angular_error
                )
            )
