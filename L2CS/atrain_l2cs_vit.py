import os 
import argparse 
import time 


import torch 
import torch.nn as nn 
from torch.autograd import Variable 
from torch.utils.data import DataLoader 
from torchvision import transforms 
import torch.backends.cudnn as cudnn 
import torchvision 

from l2cs import L2CS_ViT, select_device, Mpiigaze 


def parse_args():
    parser = argparse.ArgumentParser(description= "Gaze estimation using L2CSNet") 

    #mpiigaze 
    parser.add_argument(
        "--gazeMpiimage_dir", 
        dest = "gazeMpiimage_dir", 
        help = "Directory path for gaze images", 
        default = "datasets/MPIIFaceGaze/Image", 
        type = str,
    )

    parser.add_argument(
        "--gazeMpiilabel_dir", 
        dest = "gazeMpiilabel_dir", 
        help = "Directory path for gaze labels", 
        default = "datasets/MPIIFaceGaze/Label",
        type = str,
    )

    # Important args ----------------------------------------------------------------------------
    # -------------------------------------------------------------------------------------------

    parser.add_argument(
        "--dataset", 
        dest = "dataset", 
        help = "mpiigaze, rtgene, gaze360, ethgaze",
        default = "mpiigaze", 
        type = str,
    )

    parser.add_argument(
        "--output", 
        dest = "output", 
        help = "Path of output models",
        default= "output/snapshots/", 
        type = str,
        )
    
    parser.add_argument(
        "--gpu", 
        dest = "gpu_id", 
        help = "GPU device id to use [0] or multiply 0,1,2,3", 
        default = "0", 
        type = str,
    )

    parser.add_argument(
        "--num_epochs", 
        dest = "num_epochs", 
        help = "Maximum number of training epochs", 
        defualt = 1, 
        type = int,
    )

    parser.add_argument(
        "--batch_size", 
        dest = "batch_size", 
        help = "Batch Size", 
        default = 1, 
        type = int
    )

    parser.add_argument(
        "--arch", 
        dest = "arch", 
        help = "Network architecture, can be: L2CS_ViT, ResNet18, ResNet34, ...", 
        default = "L2CS_ViT", 
        type = str,
    )

    parser.add_argument(
        "--alpha", 
        dest = "alpha", 
        help = "Regression loss coefficient", 
        default = 1, 
        type = float, 

    )

    parser.add_argument(
        "--lr", 
        dest = "lr", 
        help = "Base learning rate", 
        default = 0.00001, 
        type = float
    )

    args = parser.parse_args()
    return args 



def get_model(arch, bins):
    assert arch == "L2CS_ViT"
    image_size = 448 
    patch_size = 16 
    num_classes = bins 
    dim = 512 
    depth = 6 
    heads = 8 
    mlp_dim = 1024 
    model = L2CS_ViT(
        image_size, patch_size, num_classes, dim, depth, heads, mlp_dim
    )
    return model 


if __name__ == "__main__":
    args = parse_args() 
    cudnn.enabled = True 
    num_epochs = args.num_epochs 
    batch_size = args.batch_size 
    data_set = args.dataset 
    alpha = args.alpha 
    output = args.output 


    transformations = transforms.Compose(
        [
            transforms.Resize(448), 
            transforms.ToTensor(), 
            transforms.Normalize(mean = [0.485, 0.456, 0.406], std = [0.229, 0.224, 0.225]), 

        ]
    )


    # Note that this code is tailored for mpiigaze dataset model training.
    assert data_set == "mpiigaze"
    folder = os.listdir(args.gazeMpiilabel_dir) 
    folder.sort()
    testlabelpathombined = [os.path.join(args.gazeMpiilabel_dir, j) for j in folder] 
    for folder in range(15):


    
    


