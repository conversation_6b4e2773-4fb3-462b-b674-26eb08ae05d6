name: l2cs
channels:
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2024.7.2=h06a4308_0
  - ld_impl_linux-64=2.38=h1181459_1
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - ncurses=6.4=h6a678d5_0
  - openssl=3.0.14=h5eee18b_0
  - pip=24.0=py310h06a4308_0
  - python=3.10.14=h955ad1f_1
  - readline=8.2=h5eee18b_0
  - setuptools=69.5.1=py310h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - tk=8.6.14=h39e8969_0
  - wheel=0.43.0=py310h06a4308_0
  - xz=5.4.6=h5eee18b_1
  - zlib=1.2.13=h5eee18b_1
  - pip:
      - absl-py==2.1.0
      - accelerate==0.32.1
      - annotated-types==0.7.0
      - certifi==2024.7.4
      - charset-normalizer==3.3.2
      - contourpy==1.2.1
      - cycler==0.12.1
      - deepspeed==0.14.4
      - einops==0.8.0
      - face-detection==1.0.5
      - filelock==3.15.4
      - fonttools==4.53.1
      - fsspec==2024.6.1
      - grpcio==1.65.1
      - hjson==3.1.0
      - huggingface-hub==0.23.5
      - idna==3.7
      - jinja2==3.1.4
      - kiwisolver==1.4.5
      - markdown==3.6
      - markupsafe==2.1.5
      - matplotlib==3.9.1
      - mpmath==1.3.0
      - networkx==3.3
      - ninja==********
      - numpy==1.26.4
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.1.105
      - nvidia-cuda-nvrtc-cu12==12.1.105
      - nvidia-cuda-runtime-cu12==12.1.105
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==*********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==**********
      - nvidia-cusparse-cu12==12.1.0.106
      - nvidia-ml-py==12.555.43
      - nvidia-nccl-cu12==2.20.5
      - nvidia-nvjitlink-cu12==12.5.82
      - nvidia-nvtx-cu12==12.1.105
      - opencv-python==*********
      - packaging==24.1
      - pandas==2.2.2
      - pillow==10.4.0
      - protobuf==4.25.4
      - psutil==6.0.0
      - py-cpuinfo==9.0.0
      - pydantic==2.8.2
      - pydantic-core==2.20.1
      - pyparsing==3.1.2
      - python-dateutil==2.9.0.post0
      - pytz==2024.1
      - pyyaml==6.0.1
      - requests==2.32.3
      - safetensors==0.4.3
      - scipy==1.14.0
      - six==1.16.0
      - sympy==1.13.0
      - tensorboard==2.17.0
      - tensorboard-data-server==0.7.2
      - tensorboardx==*******
      - timm==1.0.7
      - torch==2.3.1
      - torchaudio==2.3.1
      - torchvision==0.18.1
      - tqdm==4.66.4
      - triton==2.3.1
      - typing-extensions==4.12.2
      - tzdata==2024.1
      - urllib3==2.2.2
      - werkzeug==3.0.3
prefix: /home/<USER>/miniconda3/envs/l2cs
