import os
import argparse
import time
import copy
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torchvision import transforms
import torch.backends.cudnn as cudnn
from torch.optim.lr_scheduler import CosineAnnealingLR
from l2cs import L2CS_ViT, Mpiigaze
from tensorboardX import SummaryWriter
import pandas as pd
import json

def parse_args():
    parser = argparse.ArgumentParser(description="Gaze estimation using L2CSNet")

    parser.add_argument(
        "--gazeMpiimage_dir",
        dest="gazeMpiimage_dir",
        help="Directory path for gaze images",
        default="datasets/MPIIFaceGaze/Image",
        type=str,
    )

    parser.add_argument(
        "--gazeMpiilabel_dir",
        dest="gazeMpiilabel_dir",
        help="Directory path for gaze labels",
        default="datasets/MPIIFaceGaze/Label",
        type=str,
    )

    parser.add_argument(
        "--dataset",
        dest="dataset",
        help="mpiigaze, rtgene, gaze360, ethgaze",
        default="mpiigaze",
        type=str,
    )

    parser.add_argument(
        "--output",
        dest="output",
        help="Path of output models",
        default="output/snapshots_1GPU_PROPER/",
        type=str,
    )

    parser.add_argument(
        "--gpu",
        dest="gpu_id",
        help="GPU device id to use [0] or multiple 0,1,2,3",
        default="2",
        type=str,
    )

    parser.add_argument(
        "--num_epochs",
        dest="num_epochs",
        help="Maximum number of training epochs",
        default=1,
        type=int,
    )

    parser.add_argument(
        "--batch_size",
        dest="batch_size",
        help="Batch Size",
        default=54,
        type=int
    )

    parser.add_argument(
        "--arch",
        dest="arch",
        help="Network architecture, can be: L2CS_ViT, ResNet18, ResNet34, ...",
        default="L2CS_ViT",
        type=str,
    )

    parser.add_argument(
        "--alpha",
        dest="alpha",
        help="Regression loss coefficient",
        default=1,
        type=float,
    )

    parser.add_argument(
        "--lr",
        dest="lr",
        help="Base learning rate",
        default=0.0001,
        type=float
    )

    parser.add_argument(
        "--patience",
        dest="patience",
        help="Patience for early stopping",
        default=10,
        type=int
    )

    parser.add_argument(
        "--threshold",
        dest="threshold",
        help="Threshold for retraining on best model weights",
        default=0.01,
        type=float
    )

    args = parser.parse_args()
    return args

def get_model(arch, bins):
    assert arch == "L2CS_ViT"
    image_size = 448
    patch_size = 16
    num_classes = bins
    dim = 512
    depth = 6
    heads = 8
    mlp_dim = 1024
    model = L2CS_ViT(
        image_size, patch_size, num_classes, dim, depth, heads, mlp_dim
    )
    return model

def LR_weights_callback(model, best_model_weights, optimizer, val_mae, best_mae, current_lr, new_lr, fail_epochs):
    """ Callback weights and decrease LR """
    if val_mae < best_mae:
        fail_epochs = 0
        best_mae = val_mae
        best_model_weights = copy.deepcopy(model.state_dict())
    elif fail_epochs + 1 >= 3 and current_lr > 1e-4:
        fail_epochs = 0
        model.load_state_dict(best_model_weights)
        optimizer.param_groups[0]['lr'] = max(new_lr * 0.7, 1e-6)
        new_lr = optimizer.param_groups[0]['lr']
    elif fail_epochs + 1 >= 10 and current_lr > 1e-5:
        fail_epochs = 0
        optimizer.param_groups[0]['lr'] = max(new_lr * 0.7, 1e-6)
        new_lr = optimizer.param_groups[0]['lr']
    elif fail_epochs + 1 >= 15 and current_lr <= 1e-5:
        fail_epochs = 0
        optimizer.param_groups[0]['lr'] = max(new_lr * 0.7, 1e-6)
        new_lr = optimizer.param_groups[0]['lr']
    else:
        fail_epochs += 1

    return best_model_weights, best_mae, new_lr, fail_epochs

if __name__ == "__main__":
    args = parse_args()
    cudnn.enabled = True
    num_epochs = args.num_epochs
    batch_size = args.batch_size
    data_set = args.dataset
    alpha = args.alpha
    output = args.output
    patience = args.patience
    threshold = args.threshold

    transformations = transforms.Compose(
        [
            transforms.Resize(448),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ]
    )

    assert data_set == "mpiigaze"
    folder = os.listdir(args.gazeMpiilabel_dir)
    folder.sort()
    testlabelpathombined = [os.path.join(args.gazeMpiilabel_dir, j) for j in folder]
    device = "cuda:" + args.gpu_id

    for fold in range(15):
        model = get_model(args.arch, 28).to(device)
        train_dataset = Mpiigaze(testlabelpathombined, args.gazeMpiimage_dir, transformations, True, fold=fold)
        val_dataset = Mpiigaze(testlabelpathombined, args.gazeMpiimage_dir, transformations, False, fold=fold)

        train_loader_gaze = DataLoader(
            dataset=train_dataset,
            batch_size=int(batch_size),
            shuffle=True,
            num_workers=16,
            pin_memory=True
        )

        val_loader_gaze = DataLoader(
            dataset=val_dataset,
            batch_size=int(batch_size),
            shuffle=False,
            num_workers=16,
            pin_memory=True
        )

        optimizer_gaze = optim.AdamW(
            model.parameters(),
            lr=args.lr,
            weight_decay=1e-2,
            betas=(0.9, 0.999),
            eps=1e-8
        )

        scheduler_gaze = CosineAnnealingLR(optimizer_gaze, T_max=10)

        summary_name = "{}_{}".format("L2CS-mpiigaze", int(time.time()))
        summary_path = os.path.join(output, "{}".format(summary_name), "fold" + str(fold))
        os.makedirs(summary_path, exist_ok=True)

        writer = SummaryWriter(log_dir=summary_path)
        log_file = os.path.join(summary_path, "training_log.csv")
        with open(log_file, 'w') as f:
            f.write("epoch,iter,loss_yaw,loss_pitch,learning_rate\n")

        criterion = nn.CrossEntropyLoss().to(device)
        reg_criterion = nn.MSELoss().to(device)
        softmax = nn.Softmax(dim=1).to(device)
        idx_tensor = torch.FloatTensor([idx for idx in range(28)]).to(device)

        config = {
            "arch": args.arch,
            "num_epochs": num_epochs,
            "batch_size": batch_size,
            "learning_rate": args.lr,
            "patience": patience,
            "alpha": alpha,
            "threshold": threshold,
            "gpu_id": args.gpu_id
        }
        with open(os.path.join(summary_path, "config.json"), 'w') as f:
            json.dump(config, f, indent=4)

        best_loss = float('inf')
        best_mae = float('inf')
        best_model_weights = copy.deepcopy(model.state_dict())
        patience_counter = 0
        fail_epochs = 0

        for epoch in range(num_epochs):
            model.train()
            sum_loss_pitch_gaze = 0
            sum_loss_yaw_gaze = 0
            iter_gaze = 0

            for i, (images_gaze, labels_gaze, cont_labels_gaze, name) in enumerate(train_loader_gaze):
                pitch, yaw = model(images_gaze.to(device))

                loss_pitch_gaze = criterion(pitch, labels_gaze[:, 0].to(device))
                loss_yaw_gaze = criterion(yaw, labels_gaze[:, 1].to(device))

                pitch_predicted = softmax(pitch)
                yaw_predicted = softmax(yaw)

                pitch_predicted = torch.sum(pitch_predicted * idx_tensor, 1) * 3 - 42
                yaw_predicted = torch.sum(yaw_predicted * idx_tensor, 1) * 3 - 42

                loss_reg_pitch = reg_criterion(pitch_predicted, cont_labels_gaze[:, 0].to(device))
                loss_reg_yaw = reg_criterion(yaw_predicted, cont_labels_gaze[:, 1].to(device))

                loss_pitch_gaze += alpha * loss_reg_pitch
                loss_yaw_gaze += alpha * loss_reg_yaw

                sum_loss_pitch_gaze += loss_pitch_gaze.item()
                sum_loss_yaw_gaze += loss_yaw_gaze.item()

                optimizer_gaze.zero_grad()
                total_loss = loss_pitch_gaze + loss_yaw_gaze
                total_loss.backward()
                optimizer_gaze.step()

                iter_gaze += 1

                if (i + 1) % 100 == 0:
                    current_lr = optimizer_gaze.param_groups[0]['lr']
                    print(
                        "Epoch [%d/%d], Iter [%d/%d] Losses: "
                        "Gaze Yaw %.4f, Gaze Pitch %.4f, learning_rate: %.6f"
                        % (
                            epoch + 1,
                            num_epochs,
                            i + 1,
                            len(train_dataset) // batch_size,
                            sum_loss_yaw_gaze / iter_gaze,
                            sum_loss_pitch_gaze / iter_gaze,
                            current_lr
                        )
                    )
                    with open(log_file, 'a') as f:
                        f.write(f"{epoch + 1},{i + 1},{sum_loss_yaw_gaze / iter_gaze},{sum_loss_pitch_gaze / iter_gaze},{current_lr}\n")

            scheduler_gaze.step()

            # Validation
            model.eval()
            val_loss_pitch_gaze = 0
            val_loss_yaw_gaze = 0
            val_iter_gaze = 0
            total_angle_error = 0

            with torch.no_grad():
                for images_gaze, labels_gaze, cont_labels_gaze, name in val_loader_gaze:
                    pitch, yaw = model(images_gaze.to(device))

                    loss_pitch_gaze = criterion(pitch, labels_gaze[:, 0].to(device))
                    loss_yaw_gaze = criterion(yaw, labels_gaze[:, 1].to(device))

                    pitch_predicted = softmax(pitch)
                    yaw_predicted = softmax(yaw)

                    pitch_predicted = torch.sum(pitch_predicted * idx_tensor, 1) * 3 - 42
                    yaw_predicted = torch.sum(yaw_predicted * idx_tensor, 1) * 3 - 42

                    loss_reg_pitch = reg_criterion(pitch_predicted, cont_labels_gaze[:, 0].to(device))
                    loss_reg_yaw = reg_criterion(yaw_predicted, cont_labels_gaze[:, 1].to(device))

                    loss_pitch_gaze += alpha * loss_reg_pitch
                    loss_yaw_gaze += alpha * loss_reg_yaw

                    val_loss_pitch_gaze += loss_pitch_gaze.item()
                    val_loss_yaw_gaze += loss_yaw_gaze.item()
                    val_iter_gaze += 1

                    angle_error = torch.mean(torch.sqrt((pitch_predicted - cont_labels_gaze[:, 0].to(device)) ** 2 +
                                                        (yaw_predicted - cont_labels_gaze[:, 1].to(device)) ** 2))
                    total_angle_error += angle_error.item()

            avg_val_loss_pitch_gaze = val_loss_pitch_gaze / val_iter_gaze
            avg_val_loss_yaw_gaze = val_loss_yaw_gaze / val_iter_gaze
            avg_val_loss = (avg_val_loss_pitch_gaze + avg_val_loss_yaw_gaze) / 2
            avg_angle_error = total_angle_error / val_iter_gaze

            print(
                "Validation - Epoch [%d/%d] Losses: "
                "Gaze Yaw %.4f, Gaze Pitch %.4f, Average loss: %.4f, Angular error: %.4f"
                % (
                    epoch + 1,
                    num_epochs,
                    avg_val_loss_yaw_gaze,
                    avg_val_loss_pitch_gaze,
                    avg_val_loss,
                    avg_angle_error
                )
            )

            writer.add_scalar('Loss/train/yaw', sum_loss_yaw_gaze / iter_gaze, epoch)
            writer.add_scalar('Loss/train/pitch', sum_loss_pitch_gaze / iter_gaze, epoch)
            writer.add_scalar('Loss/val/yaw', avg_val_loss_yaw_gaze, epoch)
            writer.add_scalar('Loss/val/pitch', avg_val_loss_pitch_gaze, epoch)
            writer.add_scalar('Angular_error/val', avg_angle_error, epoch)

            best_model_weights, best_mae, new_lr, fail_epochs = LR_weights_callback(
                model, best_model_weights, optimizer_gaze, avg_angle_error, best_mae,
                optimizer_gaze.param_groups[0]['lr'], optimizer_gaze.param_groups[0]['lr'], fail_epochs
            )

            if avg_val_loss < best_loss:
                best_loss = avg_val_loss
                patience_counter = 0
                torch.save(model.state_dict(), os.path.join(summary_path, "best_model.pt"))
            else:
                patience_counter += 1

            if patience_counter >= patience:
                print(f"Early stopping at epoch {epoch + 1}")
                break

            if epoch > 0 and abs(avg_val_loss - best_loss) > threshold:
                model.load_state_dict(torch.load(os.path.join(summary_path, "best_model.pt")))

        # Save the last model weights
        torch.save(model.state_dict(), os.path.join(summary_path, "last_model.pt"))

        writer.close()
