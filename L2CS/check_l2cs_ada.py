from l2cs import L2CS_ADA 
import torchvision 
import torch.utils.model_zoo as model_zoo 
import torch 

def load_filtered_state_dict(model, snapshot):
    # By user apaszke from discuss.pytorch.org
    model_dict = model.state_dict()
    snapshot = {k: v for k, v in snapshot.items() if k in model_dict}
    model_dict.update(snapshot)
    model.load_state_dict(model_dict)

def main():
    my_model = L2CS_ADA(torchvision.models.resnet.Bottleneck, [3, 4, 6, 3], 28)
    pre_url = 'https://download.pytorch.org/models/resnet50-19c8e357.pth' 
    load_filtered_state_dict(my_model, model_zoo.load_url(pre_url))
    # Define the model
    num_bins = 28  # Example number of bins

    # Generate dummy input data
    batch_size = 8
    num_classes = 70722
    dummy_input = torch.randn(batch_size, 3, 224, 224)  # Example input size (batch_size, channels, height, width)
    dummy_labels = torch.randint(0, num_bins, (batch_size,2)) 
    out = my_model(dummy_input, dummy_labels)
    print("yaw_shape", out[0].shape)
    print("pitch_shape", out[1].shape)
    


if __name__ == "__main__":
    main()