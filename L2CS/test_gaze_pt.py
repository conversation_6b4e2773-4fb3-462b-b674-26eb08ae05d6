import os
import torch
import torch.nn as nn
import torchvision
import numpy as np
import argparse
from torch.autograd import Variable
from torch.utils.data import DataLoader
from torchvision import transforms
from l2cs import L2CS, select_device, Gaze360, gazeto3d, angular, MOBILEV4, MOBILEV4_ADA, L2CS


def parse_args():
    parser = argparse.ArgumentParser(description="Model Validation")
    parser.add_argument("--gaze360image_dir", type=str, default="preprocessing/gaze360/Image")
    parser.add_argument("--gaze360label_dir", type=str, default="preprocessing/gaze360/Label/test_TI.label")
    parser.add_argument("--model_path", type=str, default="path/to/model/last_model.pt")
    parser.add_argument("--gpu_id", type=str, default="0")
    parser.add_argument("--batch_size", type=int, default=16)
    parser.add_argument("--arch", type=str, default="MobileNetV4")
    parser.add_argument("--bins", type=int, default=90)
    parser.add_argument(
        "--bin-width",
        dest="bin_width",
        help="Gaze digitize bin width, check dataset for more info",
        default=4,
        type=int,
    )
    parser.add_argument(
        "--angle",
        dest="angle",
        help="Gaze digitize bin width, check dataset for more info",
        default=180,
        type=int,
    )
    
    return parser.parse_args()

def getArch_weights(arch, bins):
    if arch == "MobileNetV4":
        model = MOBILEV4(num_bins= bins)
        print("MobileNetV4 model is used")
    elif arch == "MobileNetV4_Ada":
        model = MOBILEV4_ADA(num_bins= bins)
        print("MobileNetV4_Ada model is used")
    else:
        model = L2CS( torchvision.models.resnet.Bottleneck, [3, 4, 6,  3], bins)
        print("ResNet50 model is used")

    return model

def load_model(model_path, arch, bins, device):
    
    model = getArch_weights(arch, bins)
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.to(device)
    model.eval()
    return model

def validate_model(model, dataloader, device, bin_width):
    total_angle_error = 0.0
    total_samples = 0
    gpu = "cuda:" + args.gpu_id
    
    softmax = nn.Softmax(dim=1).cuda(gpu)
    idx_tensor = [idx for idx in range(90)]
    idx_tensor = Variable(torch.FloatTensor(idx_tensor), requires_grad=True).cuda(gpu)

    with torch.no_grad():
        for images, labels, cont_labels, _ in dataloader:
            images = images.to(device)
            labels = labels.to(device)
            cont_labels = cont_labels.to(device)

            pitch, yaw = model(images)

            pitch_predicted = softmax(pitch)
            yaw_predicted = softmax(yaw)
            
            pitch_predicted = (
                torch.sum(pitch_predicted * idx_tensor, 1) * bin_width - args.angle
            )
            yaw_predicted = (
                torch.sum(yaw_predicted * idx_tensor, 1) * bin_width - args.angle
            )
            
            # converting to the radian.
            label_pitch = cont_labels[:, 0].float() * np.pi / 180
            label_yaw = cont_labels[:, 1].float() * np.pi / 180

            # converting the predicted to radian.
            pitch_predicted_rad = pitch_predicted * np.pi / 180
            yaw_predicted_rad = yaw_predicted * np.pi / 180
            
            pitch_predicted_np = pitch_predicted_rad.detach().cpu().numpy()
            yaw_predicted_np = yaw_predicted_rad.detach().cpu().numpy()


            for p, y, pl, yl in zip(pitch_predicted_np, yaw_predicted_np, label_pitch, label_yaw):
                total_angle_error += angular(
                    gazeto3d([p.item(), y.item()]), gazeto3d([pl.item(), yl.item()])
                )

            total_samples += cont_labels.size(0)

    mean_angular_error = total_angle_error / total_samples
    print(f"Validation completed: Mean Angular Error: {mean_angular_error:.4f}")


if __name__ == "__main__":
    args = parse_args()
    device = select_device(args.gpu_id)

    transformations = transforms.Compose([
        transforms.Resize(448),
        transforms.Grayscale(num_output_channels=3),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
    ])

    val_dataset = Gaze360(args.gaze360label_dir, args.gaze360image_dir, transformations, 180, 4, train=False)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=4, pin_memory=True)

    model = load_model(args.model_path, args.arch, args.bins, device)
    validate_model(model, val_loader, device, args.bin_width)


'''

python test_gaze_pt.py \
    --model_path output_gaze360_new_strategy_pha/snapshots/L2CS-gaze360-mobilenetv4_1729665603/best_model/last_model.pt \
        --arch MobileNetV4
'''