import torch
import torch.nn as nn
from einops import rearrange, repeat
import timm

# class RotaryPositionalEmbeddings(nn.Module):
#     def __init__(self, d: int, base: int = 10000):
#         super().__init__()
#         self.base = base
#         self.d = d
#         self.cos_cached = None
#         self.sin_cached = None

#     def _build_cache(self, seq_len, device):
#         if self.cos_cached is not None and seq_len <= self.cos_cached.shape[2]:
#             return
#         theta = 1.0 / (self.base ** (torch.arange(0, self.d, 2).float() / self.d)).to(
#             device
#         )
#         seq_idx = torch.arange(seq_len, device=device).float().to(device)
#         idx_theta = torch.einsum("n,d->nd", seq_idx, theta)
#         idx_theta2 = torch.cat([idx_theta, idx_theta], dim=1)
#         self.cos_cached = idx_theta2.cos()[None, None, :, :]
#         self.sin_cached = idx_theta2.sin()[None, None, :, :]

#     def _neg_half(self, x: torch.Tensor):
#         d_2 = self.d // 2
#         return torch.cat([-x[..., d_2:], x[..., :d_2]], dim=-1)

#     def forward(self, x: torch.Tensor):
#         b, h, seq_len, _ = x.shape
#         self._build_cache(seq_len, x.device)
#         x_rope, x_pass = x[..., : self.d], x[..., self.d :]
#         neg_half_x = self._neg_half(x_rope)
#         x_rope = (x_rope * self.cos_cached[:, :, :seq_len, :]) + (
#             neg_half_x * self.sin_cached[:, :, :seq_len, :]
#         )
#         return torch.cat((x_rope, x_pass), dim=-1)


# class RotaryPEMultiHeadAttention(nn.Module):
#     def __init__(
#         self,
#         heads: int,
#         d_model: int,
#         rope_percentage: float = 0.5,
#         dropout_prob: float = 0.0,
#     ):
#         super().__init__()
#         self.heads = heads
#         self.d_model = d_model
#         self.d_k = d_model // heads
#         self.scale = self.d_k**-0.5
#         self.qkv = nn.Linear(d_model, d_model * 3, bias=False)
#         self.dropout = nn.Dropout(dropout_prob)
#         self.proj = nn.Linear(d_model, d_model)
#         d_rope = int(self.d_k * rope_percentage)
#         self.query_rotary_pe = RotaryPositionalEmbeddings(d_rope)
#         self.key_rotary_pe = RotaryPositionalEmbeddings(d_rope)

#     def forward(self, x):
#         b, n, _ = x.shape
#         qkv = self.qkv(x).chunk(3, dim=-1)
#         query, key, value = map(
#             lambda t: rearrange(t, "b n (h d) -> b h n d", h=self.heads), qkv
#         )
#         query, key = self.query_rotary_pe(query), self.key_rotary_pe(key)
#         scores = torch.einsum("bhid,bhjd->bhij", query, key) * self.scale
#         attn = scores.softmax(dim=-1)
#         attn = self.dropout(attn)
#         out = torch.einsum("bhij,bhjd->bhid", attn, value)
#         out = rearrange(out, "b h n d -> b n (h d)")
#         return self.proj(out)


# class ViTBlock(nn.Module):
#     def __init__(self, dim, num_heads, mlp_dim, dropout=0.1):
#         super(ViTBlock, self).__init__()
#         self.attn = RotaryPEMultiHeadAttention(num_heads, dim, dropout_prob=dropout)
#         self.mlp = nn.Sequential(
#             nn.Linear(dim, mlp_dim),
#             nn.ReLU(),
#             nn.Dropout(dropout),
#             nn.Linear(mlp_dim, dim),
#             nn.Dropout(dropout),
#         )
#         self.norm1 = nn.LayerNorm(dim)
#         self.norm2 = nn.LayerNorm(dim)

#     def forward(self, x):
#         x = x + self.attn(self.norm1(x))
#         x = x + self.mlp(self.norm2(x))
#         return x


# class L2CS_ViT(nn.Module):
#     def __init__(
#         self,
#         image_size,
#         patch_size,
#         num_classes,
#         dim,
#         depth,
#         heads,
#         mlp_dim,
#         channels=3,
#         dropout=0.1,
#     ):
#         super(L2CS_ViT, self).__init__()
#         assert (
#             image_size % patch_size == 0
#         ), "Image dimensions must be divisible by the patch size."
#         num_patches = (image_size // patch_size) ** 2
#         patch_dim = channels * patch_size * patch_size

#         self.patch_size = patch_size
#         self.patch_embedding = nn.Linear(patch_dim, dim)
#         self.cls_token = nn.Parameter(torch.randn(1, 1, dim))
#         self.pos_embedding = nn.Parameter(torch.randn(1, num_patches + 1, dim))
#         self.transformer = nn.ModuleList(
#             [ViTBlock(dim, heads, mlp_dim, dropout) for _ in range(depth)]
#         )
#         self.norm = nn.LayerNorm(dim)
#         self.fc_yaw_gaze = nn.Linear(dim, num_classes)
#         self.fc_pitch_gaze = nn.Linear(dim, num_classes)

#     def forward(self, x):
#         b, c, h, w = x.shape
#         x = rearrange(
#             x,
#             "b c (h p1) (w p2) -> b (h w) (p1 p2 c)",
#             p1=self.patch_size,
#             p2=self.patch_size,
#         )
#         x = self.patch_embedding(x)
#         cls_tokens = repeat(self.cls_token, "() n d -> b n d", b=b)
#         x = torch.cat((cls_tokens, x), dim=1)
#         x += self.pos_embedding[:, : (x.size(1))]
#         x = rearrange(x, "b n d -> n b d")

#         for blk in self.transformer:
#             x = blk(x)

#         x = self.norm(x[0])  # Class token

#         pre_yaw_gaze = self.fc_yaw_gaze(x)
#         pre_pitch_gaze = self.fc_pitch_gaze(x)

#         return pre_yaw_gaze, pre_pitch_gaze


# # Model instantiation example
# image_size = 224
# patch_size = 16
# num_classes = 10  # Number of bins for gaze estimation
# dim = 512
# depth = 6
# heads = 8
# mlp_dim = 1024

# model = L2CS_ViT(image_size, patch_size, num_classes, dim, depth, heads, mlp_dim)

# # Generate a dummy input tensor with shape (batch_size, channels, height, width)
# dummy_input = torch.randn(1, 3, image_size, image_size)

# # Pass the dummy input through the model
# output_yaw, output_pitch = model(dummy_input)

# # Print the outputs
# print("Output Yaw:", output_yaw)
# print("Output Pitch:", output_pitch)




class RotaryPositionalEmbeddings(nn.Module):
    def __init__(self, d: int, base: int = 10000):
        super().__init__()
        self.base = base
        self.d = d
        self.cos_cached = None
        self.sin_cached = None

    def _build_cache(self, seq_len, device):
        if self.cos_cached is not None and seq_len <= self.cos_cached.shape[2]:
            return
        theta = 1.0 / (self.base ** (torch.arange(0, self.d, 2).float() / self.d)).to(
            device
        )
        seq_idx = torch.arange(seq_len, device=device).float().to(device)
        idx_theta = torch.einsum("n,d->nd", seq_idx, theta)
        idx_theta2 = torch.cat([idx_theta, idx_theta], dim=1)
        self.cos_cached = idx_theta2.cos()[None, None, :, :]
        self.sin_cached = idx_theta2.sin()[None, None, :, :]

    def _neg_half(self, x: torch.Tensor):
        d_2 = self.d // 2
        return torch.cat([-x[..., d_2:], x[..., :d_2]], dim=-1)

    def forward(self, x: torch.Tensor):
        b, h, seq_len, _ = x.shape
        self._build_cache(seq_len, x.device)
        x_rope, x_pass = x[..., : self.d], x[..., self.d :]
        neg_half_x = self._neg_half(x_rope)
        x_rope = (x_rope * self.cos_cached[:, :, :seq_len, :]) + (
            neg_half_x * self.sin_cached[:, :, :seq_len, :]
        )
        return torch.cat((x_rope, x_pass), dim=-1)


class RotaryPEMultiHeadAttention(nn.Module):
    def __init__(
        self,
        heads: int,
        d_model: int,
        rope_percentage: float = 0.5,
        dropout_prob: float = 0.0,
    ):
        super().__init__()
        self.heads = heads
        self.d_model = d_model
        self.d_k = d_model // heads
        self.scale = self.d_k**-0.5
        self.qkv = nn.Linear(d_model, d_model * 3, bias=False)
        self.dropout = nn.Dropout(dropout_prob)
        self.proj = nn.Linear(d_model, d_model)
        d_rope = int(self.d_k * rope_percentage)
        self.query_rotary_pe = RotaryPositionalEmbeddings(d_rope)
        self.key_rotary_pe = RotaryPositionalEmbeddings(d_rope)

    def forward(self, x):
        b, n, _ = x.shape
        qkv = self.qkv(x).chunk(3, dim=-1)
        query, key, value = map(
            lambda t: rearrange(t, "b n (h d) -> b h n d", h=self.heads), qkv
        )
        query, key = self.query_rotary_pe(query), self.key_rotary_pe(key)
        scores = torch.einsum("bhid,bhjd->bhij", query, key) * self.scale
        attn = scores.softmax(dim=-1)
        attn = self.dropout(attn)
        out = torch.einsum("bhij,bhjd->bhid", attn, value)
        out = rearrange(out, "b h n d -> b n (h d)")
        return self.proj(out)


class ViTBlock(nn.Module):
    def __init__(self, dim, num_heads, mlp_dim, dropout=0.1):
        super(ViTBlock, self).__init__()
        self.attn = RotaryPEMultiHeadAttention(num_heads, dim, dropout_prob=dropout)
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(mlp_dim, dim),
            nn.Dropout(dropout),
        )
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)

    def forward(self, x):
        x = x + self.attn(self.norm1(x))
        x = x + self.mlp(self.norm2(x))
        return x


class L2CS_ViT(nn.Module):
    def __init__(
        self,
        image_size,
        patch_size,
        num_classes,
        dim,
        depth,
        heads,
        mlp_dim,
        channels=3,
        dropout=0.1,
    ):
        super(L2CS_ViT, self).__init__()
        assert (
            image_size % patch_size == 0
        ), "Image dimensions must be divisible by the patch size."
        num_patches = (image_size // patch_size) ** 2
        patch_dim = channels * patch_size * patch_size

        self.image_size = image_size
        self.patch_size = patch_size
        self.patch_embedding = nn.Linear(patch_dim, dim)
        self.cls_token = nn.Parameter(torch.randn(1, 1, dim))
        self.pos_embedding = nn.Parameter(torch.randn(1, num_patches + 1, dim))
        self.transformer = nn.ModuleList(
            [ViTBlock(dim, heads, mlp_dim, dropout) for _ in range(depth)]
        )
        self.norm = nn.LayerNorm(dim)
        self.fc_yaw_gaze = nn.Linear(dim, num_classes)
        self.fc_pitch_gaze = nn.Linear(dim, num_classes)

    def forward(self, x):
        b, c, h, w = x.shape
        assert h == self.image_size and w == self.image_size, "Input image size must match model's image size."
        
        x = rearrange(
            x,
            "b c (h p1) (w p2) -> b (h w) (p1 p2 c)",
            p1=self.patch_size,
            p2=self.patch_size,
        )
        
        x = self.patch_embedding(x)
        cls_tokens = repeat(self.cls_token, "() n d -> b n d", b=b)
        x = torch.cat((cls_tokens, x), dim=1)
        x += self.pos_embedding[:, : (x.size(1))]
        
        for blk in self.transformer:
            x = blk(x)
        
        x = self.norm(x[:, 0])  # Class token
        
        pre_yaw_gaze = self.fc_yaw_gaze(x)
        pre_pitch_gaze = self.fc_pitch_gaze(x)
        
        return pre_yaw_gaze, pre_pitch_gaze


class L2CS_EfficientViT(nn.Module):
    def __init__(self, pretrained_model_name='efficientvit_b0.r224_in1k', num_classes=28):
        super(L2CS_EfficientViT, self).__init__()
        # Load the pretrained model
        self.backbone = timm.create_model(pretrained_model_name, pretrained=True, num_classes=0)
        self.num_features = self.backbone.num_features

        # Define new layers for gaze estimation
        self.global_pool = nn.AdaptiveAvgPool2d((1, 1))
        self.fc_yaw_gaze = nn.Linear(self.num_features, num_classes)
        self.fc_pitch_gaze = nn.Linear(self.num_features, num_classes)

    def forward(self, x):
        # Forward through the backbone
        x = self.backbone.forward_features(x)
        
        # Global average pooling
        x = self.global_pool(x).view(x.size(0), -1)

        # Gaze estimation heads
        pre_yaw_gaze = self.fc_yaw_gaze(x)
        pre_pitch_gaze = self.fc_pitch_gaze(x)

        return pre_yaw_gaze, pre_pitch_gaze