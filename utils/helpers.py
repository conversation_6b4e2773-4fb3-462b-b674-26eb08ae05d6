import cv2
import numpy as np

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torchvision import transforms

from utils.datasets import Gaze360, MPIIGaze

from models import (
    resnet18,
    resnet34,
    resnet50,
    mobilenet_v2,
    mobileone_s0,
    mobileone_s1,
    mobileone_s2,
    mobileone_s3,
    mobileone_s4
)


def get_model(arch, bins, pretrained=False, inference_mode=False):
    """Return the model based on the specified architecture."""
    if arch == 'resnet18':
        model = resnet18(pretrained=pretrained, num_classes=bins)
    elif arch == 'resnet34':
        model = resnet34(pretrained=pretrained, num_classes=bins)
    elif arch == 'resnet50':
        model = resnet50(pretrained=pretrained, num_classes=bins)
    elif arch == "mobilenetv2":
        model = mobilenet_v2(pretrained=pretrained, num_classes=bins)
    elif arch == "mobileone_s0":
        model = mobileone_s0(pretrained=pretrained, num_classes=bins, inference_mode=inference_mode)
    elif arch == "mobileone_s1":
        model = mobileone_s1(pretrained=pretrained, num_classes=bins, inference_mode=inference_mode)
    elif arch == "mobileone_s2":
        model = mobileone_s2(pretrained=pretrained, num_classes=bins, inference_mode=inference_mode)
    elif arch == "mobileone_s3":
        model = mobileone_s3(pretrained=pretrained, num_classes=bins, inference_mode=inference_mode)
    elif arch == "mobileone_s4":
        model = mobileone_s4(pretrained=pretrained, num_classes=bins, inference_mode=inference_mode)
    else:
        raise ValueError(f"Please choose available model architecture, currently chosen: {arch}")
    return model


import torch
import torch.nn.functional as F
def pitchyaw_to_3d_vector(pitchyaw: torch.Tensor) -> torch.Tensor:
    """
    Convert 2D pitch and yaw angles to 3D unit direction vectors.

    Args:
        pitchyaw: Tensor of shape (N, 2), where [:, 0] = pitch and [:, 1] = yaw (in radians)

    Returns:
        Tensor of shape (N, 3) containing 3D unit direction vectors
    """
    pitch = pitchyaw[:, 0]
    yaw = pitchyaw[:, 1]

    x = -torch.cos(pitch) * torch.sin(yaw)
    y = -torch.sin(pitch)
    z = -torch.cos(pitch) * torch.cos(yaw)

    return torch.stack([x, y, z], dim=1)


def angular_error(gaze_vector, label_vector):
    dot_product = np.dot(gaze_vector, label_vector)
    norm_product = np.linalg.norm(gaze_vector) * np.linalg.norm(label_vector)
    cosine_similarity = min(dot_product / norm_product, 0.9999999)

    return np.degrees(np.arccos(cosine_similarity))


def angular_loss(labels: torch.Tensor, outputs: torch.Tensor, eps: float = 1e-6) -> torch.Tensor:
    """
    Compute the mean angular loss between predicted and ground truth gaze directions.

    This function converts pitch-yaw pairs to 3D vectors and calculates the angular
    difference between them using the cosine similarity with numerical stability improvements.

    Args:
        labels: Ground truth gaze angles (pitch, yaw) of shape (N, 2)
        outputs: Predicted gaze angles (pitch, yaw) of shape (N, 2)
        eps: Small epsilon value for numerical stability

    Returns:
        Mean angular error in radians as a scalar tensor
    """
    labels = torch.from_numpy(labels).unsqueeze(0)
    outputs = torch.from_numpy(outputs).unsqueeze(0)
    
    # Clamp input angles to reasonable ranges to prevent extreme values
    labels = torch.clamp(labels, -torch.pi, torch.pi)
    outputs = torch.clamp(outputs, -torch.pi, torch.pi)

    # Normalize vectors to ensure they are unit vectors
    labels_vec = F.normalize(labels, p=2, dim=1, eps=eps)
    outputs_vec = F.normalize(outputs, p=2, dim=1, eps=eps)

    # Compute cosine similarity with additional safety
    cosine_sim = F.cosine_similarity(outputs_vec, labels_vec, dim=1)

    # Clamp to valid range for arccos with tighter bounds for numerical stability
    cosine_sim = torch.clamp(cosine_sim, -1.0 + eps, 1.0 - eps)

    # Compute angular error
    angular_error_rad = torch.arccos(cosine_sim)

    return torch.rad2deg(angular_error_rad).mean()#angular_error_rad.mean()


def calc_angle_error(labels: torch.Tensor, outputs: torch.Tensor) -> torch.Tensor:
    """
    Calculate the angular error between predicted and ground truth gaze directions in degrees.

    Args:
        labels: Ground truth gaze angles (pitch, yaw) of shape (N, 2)
        outputs: Predicted gaze angles (pitch, yaw) of shape (N, 2)

    Returns:
        Mean angular error in degrees as a scalar tensor
    """
    labels = torch.from_numpy(labels).unsqueeze(0)
    outputs = torch.from_numpy(outputs).unsqueeze(0)

    labels_norm = labels / torch.linalg.norm(labels, axis=1).reshape((-1, 1))
    outputs_norm = outputs / torch.linalg.norm(outputs, axis=1).reshape((-1, 1))

    angles = F.cosine_similarity(outputs_norm, labels_norm, dim=1)
    angles = torch.clip(angles, -1.0, 1.0)  # clamp values to [-1.0, 1.0] to avoid instability

    rad = torch.arccos(angles)
    return torch.rad2deg(rad).mean()


def gaze_to_3d(gaze):
    yaw = gaze[0]   # Horizontal angle
    pitch = gaze[1]  # Vertical angle

    gaze_vector = np.zeros(3)
    gaze_vector[0] = -np.cos(pitch) * np.sin(yaw)
    gaze_vector[1] = -np.sin(pitch)
    gaze_vector[2] = -np.cos(pitch) * np.cos(yaw)

    return gaze_vector


def get_dataloader(params,  mode="train"):
    """Load dataset and return DataLoader."""

    transform = transforms.Compose([
        transforms.Resize(448),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    if params.dataset == "gaze360":
        dataset = Gaze360(params.data, transform, angle=params.angle, binwidth=params.binwidth, mode=mode)
    elif params.dataset == "mpiigaze":
        dataset = MPIIGaze(params.data, transform, angle=params.angle, binwidth=params.binwidth)
    else:
        raise ValueError("Supported dataset are `gaze360` and `mpiigaze`")

    data_loader = DataLoader(
        dataset=dataset,
        batch_size=params.batch_size,
        shuffle=True if mode == "train" else False,
        num_workers=params.num_workers,
        pin_memory=True
    )
    return data_loader


def draw_gaze(frame, bbox, pitch, yaw, thickness=2, color=(0, 0, 255)):
    """Draws gaze direction on a frame given bounding box and gaze angles."""
    # Unpack bounding box coordinates
    x_min, y_min, x_max, y_max = map(int, bbox[:4])

    # Calculate center of the bounding box
    x_center = (x_min + x_max) // 2
    y_center = (y_min + y_max) // 2

    # Handle grayscale frames by converting them to BGR
    if len(frame.shape) == 2 or frame.shape[2] == 1:
        frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)

    # Calculate the direction of the gaze
    length = x_max - x_min
    dx = int(-length * np.sin(pitch) * np.cos(yaw))
    dy = int(-length * np.sin(yaw))

    point1 = (x_center, y_center)
    point2 = (x_center + dx, y_center + dy)

    # Draw gaze direction
    cv2.circle(frame, (x_center, y_center), radius=4, color=color, thickness=-1)
    cv2.arrowedLine(
        frame,
        point1,
        point2,
        color=color,
        thickness=thickness,
        line_type=cv2.LINE_AA,
        tipLength=0.25
    )


def draw_bbox(image, bbox, color=(0, 255, 0), thickness=2, proportion=0.2):
    x_min, y_min, x_max, y_max = map(int, bbox[:4])

    width = x_max - x_min
    height = y_max - y_min

    corner_length = int(proportion * min(width, height))

    # Draw the rectangle
    cv2.rectangle(image, (x_min, y_min), (x_max, y_max), color, 1)

    # Top-left corner
    cv2.line(image, (x_min, y_min), (x_min + corner_length, y_min), color, thickness)
    cv2.line(image, (x_min, y_min), (x_min, y_min + corner_length), color, thickness)

    # Top-right corner
    cv2.line(image, (x_max, y_min), (x_max - corner_length, y_min), color, thickness)
    cv2.line(image, (x_max, y_min), (x_max, y_min + corner_length), color, thickness)

    # Bottom-left corner
    cv2.line(image, (x_min, y_max), (x_min, y_max - corner_length), color, thickness)
    cv2.line(image, (x_min, y_max), (x_min + corner_length, y_max), color, thickness)

    # Bottom-right corner
    cv2.line(image, (x_max, y_max), (x_max, y_max - corner_length), color, thickness)
    cv2.line(image, (x_max, y_max), (x_max - corner_length, y_max), color, thickness)


def draw_bbox_gaze(frame: np.ndarray, bbox, pitch, yaw):
    draw_bbox(frame, bbox)
    draw_gaze(frame, bbox, pitch, yaw)
