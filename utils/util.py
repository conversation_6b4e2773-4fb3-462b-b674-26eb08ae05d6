import os
import sys
import cv2
import time
import numpy
import torch
import random

from pathlib import Path
from timeit import timeit
from loguru import logger
from functools import wraps
from platform import system
from datetime import datetime


def init_deterministic_seed(seed=42, deterministic_mode=True):
    """ Setup random seed """
    random.seed(seed)
    numpy.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.benchmark = not deterministic_mode
    torch.backends.cudnn.deterministic = deterministic_mode
    torch.use_deterministic_algorithms(deterministic_mode)  # (optional), but enforces stricter control
    os.environ['CUBLAS_WORKSPACE_CONFIG'] = ':4096:8'   # [':4096:8', ':16:8'] for reproducibility


def timer(func):
    """ Time decorator """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        time_taken = (end_time - start_time)

        if time_taken > 60:
            time_taken = f"{time_taken / 60.0:.2f} minutes"
        elif time_taken > 3600:
            time_taken = f"{time_taken / 3600.0:.2f} hours"
        elif time_taken > 86400:
            time_taken = f"{time_taken / 86400.0:.2f} days"
        else:
            time_taken = f"{time_taken:.2f} seconds"
        logger.warning(f"Function '{func.__name__}' took {time_taken} to complete.")
        return result
    return wrapper


def setup_multi_processes():
    """ Setup multi-processing environment variables """

    # Set multiprocess start method as `fork` to speed up the training
    if system() != 'Windows':
        torch.multiprocessing.set_start_method('fork', force=True)

    # Disable opencv multithreading to avoid system being overloaded (incompatible with PyTorch DataLoader)
    cv2.setNumThreads(0)

    # Setup OMP threads
    if 'OMP_NUM_THREADS' not in os.environ:
        os.environ['OMP_NUM_THREADS'] = '16'

    # Setup MKL threads
    if 'MKL_NUM_THREADS' not in os.environ:
        os.environ['MKL_NUM_THREADS'] = '16'


def evaluate_threads_runtime(max_num_threads=99):
    """ Evaluate how a runtime of matrix multiplication changes with the number of threads """
    runtimes = []
    threads = [1] + [t for t in range(2, max_num_threads, 2)]

    for i, thread in enumerate(threads):
        # Set current number of threads
        torch.set_num_threads(thread)

        # Calculate runtime for matrix multiplication
        runtime = timeit(
            setup="import torch; x = torch.randn(1024, 1024); y = torch.randn(1024, 1024)",
            stmt="torch.mm(x, y)",
            number=100,
        )
        # Save and show current results
        runtimes.append(runtime)
        logger.info(f"index={i},\tthread={thread},\truntime={runtime}")

    # Show funal results
    index = runtimes.index(min(runtimes))
    logger.info(f"index={index},\tthread={threads[index]},\truntime={runtimes[index]}\tResult!")


def setup_logger(log_name='exp'):
    """ Setup a logger environments for different purposes

    LEVELS = [TRACE, DEBUG, INFO, SUCCESS, WARNING, ERROR, CRITICAL]
    show messages:
        logger.trace("A trace message.")
        logger.debug("A debug message.")
        logger.info("An info message.")
        logger.success("A success message.")
        logger.warning("A warning message.")
        logger.error("An error message.")
        logger.critical("A critical message.")

        colorize=None --> the choice is automatically made based on the sink being a tty or not.
    """
    folder = "loggers"
    Path(folder).mkdir(parents=True, exist_ok=True)
    cur_date_time = datetime.now().strftime("%d.%m.%Y-%H-%M-%S")

    # For terminal - configuration to stderr (Optionally)
    logger.remove(0)    # To remove default version of logger
    default_format = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    logger.add(sink=sys.stderr, level='INFO', format=default_format, filter=None, colorize=None, serialize=False, backtrace=True, diagnose=True, enqueue=False, context=None, catch=True)

    # For logger file - configuration
    log_path = os.path.join(folder, f"{log_name}_{cur_date_time}.log")
    log_format = "{time:YYYY-MM-DD HH:mm:ss} | <level>{level: <8}</level> | <level>{message}</level>"
    logger.add(sink=log_path, level="TRACE", format=log_format, colorize=None, rotation="10 MB")
    

if __name__ == '__main__':
    # setup_logger(log_name="test")
    evaluate_threads_runtime()
