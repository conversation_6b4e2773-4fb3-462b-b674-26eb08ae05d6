import os
import torch
import skimage.io
import numpy as np
import pandas as pd
import albumentations as A
from albumentations.pytorch import ToTensorV2
from torch.utils.data import Dataset, DataLoader

from utils import util


class CustomGazeDataset(Dataset):
    """
    PyTorch Dataset for normalized gaze tracking data.

    This dataset loads face and eye images along with corresponding gaze labels
    from a structured directory format. It supports data augmentation through
    Albumentations transforms.

    Expected directory structure:
        <data_root>/
        ├── labels.csv
        └── pXX/
            ├── <basename>-face.jpg
            ├── <basename>-left-eye.jpg
            └── <basename>-right-eye.jpg

    CSV format:
        - face_file_name: Relative path to face image
        - left_eye: Relative path to left eye image
        - right_eye: Relative path to right eye image
        - pitch: Gaze pitch angle in radians
        - yaw: Gaze yaw angle in radians

    Args:
        data_path: Path to the data directory containing labels.csv and image folders
        transform: Albumentations transform pipeline to apply to images
    """
    def __init__(self, data_path: str, transform=None, angle=42, binwidth=3):
        self.data_path = data_path
        self.transform = transform
        self.angle = angle
        self.binwidth = binwidth
        self.df = pd.read_csv(f"{data_path}/labels.csv")

        # Filter out samples with angles outside the desired range
        # Convert radians to degrees for filtering
        pitch_deg = np.degrees(self.df['pitch'])
        yaw_deg = np.degrees(self.df['yaw'])

        valid_indices = (abs(pitch_deg) <= self.angle) & (abs(yaw_deg) <= self.angle)
        self.orig_list_len = len(self.df)
        self.df = self.df[valid_indices].reset_index(drop=True)

        removed_items = self.orig_list_len - len(self.df)
        print(f"{removed_items} items removed from dataset that have an angle > {self.angle}")

    def __len__(self):
        return len(self.df)

    def __getitem__(self, idx):
        row = self.df.iloc[idx]

        # Load face image
        face = skimage.io.imread(f"{self.data_path}/{row.face_file_name}")

        # Apply transforms
        if self.transform:
            face_tensor = self.transform(image=face)["image"]
        else:
            # Default conversion if no transform provided
            face_tensor = torch.from_numpy(face.transpose(2, 0, 1)).float() / 255.0

        # Get labels (already in radians from CSV)
        pitch = row.pitch
        yaw = row.yaw

        # Convert to degrees for binning
        pitch_deg = np.degrees(pitch)
        yaw_deg = np.degrees(yaw)

        # Bin values
        bins = np.arange(-self.angle, self.angle, self.binwidth)
        binned_pose = np.digitize([pitch_deg, yaw_deg], bins) - 1

        # Create binned and regression labels
        binned_labels = torch.tensor(binned_pose, dtype=torch.long)
        regression_labels = torch.tensor([pitch_deg, yaw_deg], dtype=torch.float32)

        return face_tensor, binned_labels, regression_labels


def get_dataloaders(params, val_split=0.1):
    """
    Create train, validation, and test dataloaders for gaze tracking.

    This function properly handles dataset filtering by creating the dataset first,
    then splitting based on the filtered length to avoid index out-of-bounds errors.

    Args:
        params: Parameter object containing:
            - data: Root directory containing 'train' and 'test' subdirectories
            - batch_size: Batch size for all dataloaders
            - num_workers: Number of worker processes for data loading
            - angle: Maximum angle in degrees for filtering samples
            - binwidth: Width of angle bins for classification

    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    data_root = params.data
    batch_size = params.batch_size
    num_workers = params.num_workers

    angle = params.angle
    binwidth = params.binwidth

    train_data_path = os.path.join(data_root, 'train')
    test_data_path = os.path.join(data_root, 'test')

    # Create val, test transforms
    transform_train = transform_val = transform_test = A.Compose([
        A.Normalize(),
        ToTensorV2()
    ])

    # Create the full training dataset first to get the correct filtered length
    full_train_dataset = CustomGazeDataset(train_data_path, transform_train, angle, binwidth)
    full_val_dataset = CustomGazeDataset(train_data_path, transform_val, angle, binwidth)
    test_dataset = CustomGazeDataset(test_data_path, transform_test, angle, binwidth)
    
    # Split dataset based on the filtered dataset length
    n = len(full_train_dataset)  # This is the length after filtering
    indices = list(range(n))
    np.random.shuffle(indices)
    val_size = int(n * val_split)

    val_idx = indices[:val_size]
    train_idx = indices[val_size:]

    # Create three datasets with different transforms
    train_dataset = torch.utils.data.Subset(full_train_dataset, train_idx)
    val_dataset = torch.utils.data.Subset(full_val_dataset, val_idx)

    # Create dataloaders
    train_loader = DataLoader(train_dataset,
                              batch_size=batch_size,
                              shuffle=True,
                              num_workers=num_workers,
                              pin_memory=True,
                              drop_last=True,
                              worker_init_fn=util.seed_worker,
                              generator=util.g)
    val_loader = DataLoader(val_dataset,
                            batch_size=batch_size,
                            shuffle=False,
                            num_workers=num_workers,
                            pin_memory=True,
                            worker_init_fn=util.seed_worker,
                            generator=util.g)
    test_loader = DataLoader(test_dataset,
                             batch_size=batch_size,
                             shuffle=False,
                             num_workers=num_workers,
                             pin_memory=True,
                             worker_init_fn=util.seed_worker,
                             generator=util.g)

    return train_loader, val_loader, test_loader
