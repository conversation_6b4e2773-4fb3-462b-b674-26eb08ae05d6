import os
import time
import logging
import argparse
import numpy as np
from tqdm import tqdm

os.environ['CUBLAS_WORKSPACE_CONFIG'] = ':4096:8'

import torch
import torch.nn as nn
import torch.nn.functional as F

from utils import util
from config import data_config
from utils.datasets_v3 import get_dataloaders
from utils.helpers import get_model, angular_error, pitchyaw_to_gaze_vector


# util.setup_logger()
util.setup_multi_processes()
util.init_deterministic_seed()


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')


def parse_args():
    """Parse input arguments."""
    parser = argparse.ArgumentParser(description="Gaze estimation training")
    parser.add_argument("--data", type=str, default="data/dataset_normalized_v3", help="Directory path for gaze images.")
    parser.add_argument("--data-config", type=str, default="max", help="Dataset name, available `gaze360`, `mpiigaze`, `max`.")
    parser.add_argument("--output", type=str, default="output/", help="Path of output models.")
    parser.add_argument("--checkpoint", type=str, default="", help="Path to checkpoint for resuming training.")
    parser.add_argument("--num-epochs", type=int, default=100, help="Maximum number of training epochs.")
    parser.add_argument("--batch-size", type=int, default=16, help="Batch size.")
    parser.add_argument("--arch", type=str, default="resnet18",
                        help="Network architecture, currently available: resnet18/34/50, mobilenetv2, mobileone_s0-s4.")
    parser.add_argument("--alpha", type=float, default=1, help="Regression loss coefficient.")
    parser.add_argument("--lr", type=float, default=0.001, help="Base learning rate.")
    parser.add_argument("--num-workers", type=int, default=8, help="Number of workers for data loading.")
    args = parser.parse_args()

    # Override default values based on selected dataset
    if args.data_config in data_config:
        dataset_config = data_config[args.data_config]
        args.bins = dataset_config["bins"]
        args.binwidth = dataset_config["binwidth"]
        args.angle = dataset_config["angle"]
    else:
        raise ValueError(f"Unknown dataset: {args.data_config}. Available options: {list(data_config.keys())}")

    return args


def initialize_model(args, device):
    """
    Initialize the gaze estimation model, optimizer, and optionally load a checkpoint.

    Args:
        args (argparse.Namespace): Parsed command-line arguments.
        device (torch.device): Device to load the model and optimizer onto.

    Returns:
        Tuple[nn.Module, torch.optim.Optimizer, int]: Initialized model, optimizer, and the starting epoch.
    """
    model = get_model(args.arch, args.bins, pretrained=True)
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr)
    start_epoch = 0

    if args.checkpoint:
        checkpoint = torch.load(args.checkpoint, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        # Move optimizer states to device
        for state in optimizer.state.values():
            for k, v in state.items():
                if isinstance(v, torch.Tensor):
                    state[k] = v.to(device)

        start_epoch = checkpoint['epoch']
        logging.info(f'Resumed training from {args.checkpoint}, starting at epoch {start_epoch + 1}')

    return model.to(device), optimizer, start_epoch


def train_one_epoch(
    args,
    model,
    cls_criterion,
    reg_criterion,
    optimizer,
    data_loader,
    idx_tensor,
    device,
    epoch
):
    """
    Train the model for one epoch.

    Args:
        args (argparse.Namespace): Parsed command-line arguments.
        model (nn.Module): The gaze estimation model with eye coordinate prediction.
        cls_criterion (nn.Module): Loss function for classification.
        reg_criterion (nn.Module): Loss function for regression.
        optimizer (torch.optim.Optimizer): Optimizer for the model.
        data_loader (DataLoader): DataLoader for the training dataset.
        idx_tensor (torch.Tensor): Tensor representing bin indices.
        device (torch.device): Device to perform training on.
        epoch (int): The current epoch number.

    Returns:
        Tuple[float, float, float]: Average losses for pitch, yaw, and total.
    """

    model.train()
    num_batches = len(data_loader)
    sum_loss_pitch, sum_loss_yaw, sum_loss = 0, 0, 0

    for images, binned_labels, regression_labels, regression_eyes_labels in tqdm(data_loader, total=num_batches):
        images = images.to(device)

        # Binned labels
        binned_label_pitch = binned_labels[:, 0].to(device)
        binned_label_yaw = binned_labels[:, 1].to(device)

        # Regression labels (degrees)
        regression_label_pitch = regression_labels[:, 0].to(device)
        regression_label_yaw = regression_labels[:, 1].to(device)

        # Regression eyes coordinates labels
        regression_label_left_eye_x = regression_eyes_labels[:, 0].to(device)
        regression_label_left_eye_y = regression_eyes_labels[:, 1].to(device)
        regression_label_right_eye_x = regression_eyes_labels[:, 2].to(device)
        regression_label_right_eye_y = regression_eyes_labels[:, 3].to(device)

        # Inference
        binned_pitch, binned_yaw, left_eye_pred, right_eye_pred = model(images)

        # Cross Entropy Loss
        loss_pitch = cls_criterion(binned_pitch, binned_label_pitch)
        loss_yaw = cls_criterion(binned_yaw, binned_label_yaw)

        # Softmax
        binned_pitch = F.softmax(binned_pitch, dim=1)
        binned_yaw = F.softmax(binned_yaw, dim=1)

        # Mapping from binned (0 to 90) to angels (-180° to 180°)
        pitch = torch.sum(binned_pitch * idx_tensor, 1) * args.binwidth - args.angle
        yaw = torch.sum(binned_yaw * idx_tensor, 1) * args.binwidth - args.angle

        # Mean Squared Error Loss for gaze
        loss_regression_pitch = reg_criterion(pitch, regression_label_pitch)
        loss_regression_yaw = reg_criterion(yaw, regression_label_yaw)

        # Mean Squared Error Loss for eye coordinates
        loss_left_eye_x = reg_criterion(left_eye_pred[:, 0], regression_label_left_eye_x)
        loss_left_eye_y = reg_criterion(left_eye_pred[:, 1], regression_label_left_eye_y)
        loss_right_eye_x = reg_criterion(right_eye_pred[:, 0], regression_label_right_eye_x)
        loss_right_eye_y = reg_criterion(right_eye_pred[:, 1], regression_label_right_eye_y)

        # Calculate loss with regression alpha
        loss_pitch += args.alpha * loss_regression_pitch
        loss_yaw += args.alpha * loss_regression_yaw

        # Total eye coordinate loss
        loss_eyes = loss_left_eye_x + loss_left_eye_y + loss_right_eye_x + loss_right_eye_y

        # ==================================================================
        loss_seq = [loss_pitch, loss_yaw, loss_eyes]
        grad_seq = [
            torch.tensor(1.0, device=device),  # pitch weight
            torch.tensor(1.0, device=device),  # yaw weight
            torch.tensor(1.0, device=device)   # eye coordinates weight
        ]

        optimizer.zero_grad()
        torch.autograd.backward(loss_seq, grad_seq)
        optimizer.step()
        # ==================================================================

        # # Total loss for pitch, yaw, and eye coordinates
        loss = loss_pitch + loss_yaw + loss_eyes
        
        sum_loss_pitch += loss_pitch.item()
        sum_loss_yaw += loss_yaw.item()
        sum_loss += loss.item()

    avg_loss_pitch = sum_loss_pitch / num_batches
    avg_loss_yaw = sum_loss_yaw / num_batches
    avg_loss = (sum_loss / 2) / num_batches

    return avg_loss_pitch, avg_loss_yaw, avg_loss


@torch.no_grad()
def evaluate(args, model, data_loader, idx_tensor, device, mode="val"):
    """
    Evaluate the model on the test dataset.

    Args:
        args (argparse.Namespace): Parsed command-line arguments.
        model (nn.Module): The gaze estimation model with eye coordinate prediction.
        data_loader (torch.utils.data.DataLoader): DataLoader for the test dataset.
        idx_tensor (torch.Tensor): Tensor representing bin indices.
        device (torch.device): Device to perform evaluation on.
        mode (str): Evaluation mode ("val" or "test").

    Returns:
        float: Average angular error for gaze estimation.
    """
    model.eval()
    sum_eye_error = 0
    sum_angular_error = 0
    total_num_samples = 0
    num_batches = len(data_loader)

    for images, _, regression_labels, regression_eyes_labels in tqdm(data_loader, total=num_batches):
        images = images.to(device)

        # Regression labels (degrees)
        regression_label_pitch = np.radians(regression_labels[:, 0].cpu().numpy(), dtype=np.float32)
        regression_label_yaw = np.radians(regression_labels[:, 1].cpu().numpy(), dtype=np.float32)

        # Eye coordinate labels
        eye_labels = regression_eyes_labels.cpu().numpy()

        # Inference
        binned_pitch, binned_yaw, left_eye_pred, right_eye_pred = model(images)

        # Regression predictions
        binned_pitch = F.softmax(binned_pitch, dim=1)
        binned_yaw = F.softmax(binned_yaw, dim=1)

        # Mapping from binned (0 to 90) to angles (-180° to 180°) or (0 to 28) to angles (-42°, 42°)
        pitch = torch.sum(binned_pitch * idx_tensor, 1) * args.binwidth - args.angle
        yaw = torch.sum(binned_yaw * idx_tensor, 1) * args.binwidth - args.angle

        pitch = np.radians(pitch.cpu().numpy())
        yaw = np.radians(yaw.cpu().numpy())

        # Eye coordinate predictions
        left_eye_pred_np = left_eye_pred.cpu().numpy()
        right_eye_pred_np = right_eye_pred.cpu().numpy()

        total_num_samples += images.size(0)
        for p, y, pl, yl in zip(pitch, yaw, regression_label_pitch, regression_label_yaw):
            sum_angular_error += angular_error(
                pitchyaw_to_gaze_vector(p, y), 
                pitchyaw_to_gaze_vector(pl, yl)
            )
        
        # Calculate eye coordinate error (Euclidean distance) - vectorized
        left_eye_errors = np.sqrt((left_eye_pred_np[:, 0] - eye_labels[:, 0])**2 +
                                  (left_eye_pred_np[:, 1] - eye_labels[:, 1])**2)
        right_eye_errors = np.sqrt((right_eye_pred_np[:, 0] - eye_labels[:, 2])**2 +
                                   (right_eye_pred_np[:, 1] - eye_labels[:, 3])**2)
        batch_eye_errors = (left_eye_errors + right_eye_errors) / 2
        sum_eye_error += batch_eye_errors.sum()

    avg_angular_error = sum_angular_error / total_num_samples
    avg_eye_error = sum_eye_error / total_num_samples
    
    if mode == "test":
        logging.info(
            f"Total Number of Samples: {total_num_samples} | "
            f"\nAngular Vector Error: {avg_angular_error:.7f}°"
            f"\nAverage Eye Coordinate Error: {avg_eye_error:.7f}"
        )
    
    return avg_angular_error


def main():
    args = parse_args()

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    summary_name = f'{args.data_config}_{args.arch}_{int(time.time())}'
    output = os.path.join(args.output, summary_name)
    if not os.path.exists(output):
        os.makedirs(output)

    # Initialize model, optimizer, and optionally load a checkpoint
    model, optimizer, start_epoch = initialize_model(args, device)

    # Load dataloaders with configurable augmentation
    train_loader, val_loader, test_loader = get_dataloaders(args)

    # Loss functions and index tensor
    cls_criterion = nn.CrossEntropyLoss()
    reg_criterion = nn.MSELoss()
    idx_tensor = torch.arange(args.bins, device=device, dtype=torch.float32)

    best_error = float('inf')
    print(f"Started training from epoch: {start_epoch + 1}")

    for epoch in range(start_epoch, args.num_epochs):
        avg_loss_pitch, avg_loss_yaw, avg_loss = train_one_epoch(
            args,
            model,
            cls_criterion,
            reg_criterion,
            optimizer,
            train_loader,
            idx_tensor,
            device,
            epoch
        )

        # Evaluate on validation set for the current fold
        avg_angular_error = evaluate(args, model, val_loader, idx_tensor, device, mode="val")  # Returns average error

        logging.info(
            f'Epoch [{epoch + 1}/{args.num_epochs}] '
            f'Losses:   Yaw {avg_loss_yaw:.1f},   Pitch {avg_loss_pitch:.1f},   Avg {avg_loss:.1f}  |  '
            f'Angular Vector Error: {avg_angular_error:.1f}°'
        )

        # checkpoint_path = os.path.join(output, "checkpoint.ckpt")
        # torch.save({
        #     'epoch': epoch + 1,
        #     'model_state_dict': model.state_dict(),
        #     'optimizer_state_dict': optimizer.state_dict(),
        #     'loss': avg_loss_pitch + avg_loss_yaw,
        # }, checkpoint_path)
        # logging.info(f'Checkpoint saved at {checkpoint_path}')

        if avg_angular_error < best_error:
            best_error = avg_angular_error
            best_model_path = os.path.join(output, 'best_model.pt')
            torch.save(model.state_dict(), best_model_path)
            logging.info(f'Best model saved at {best_model_path}')

    # Test the best model
    model = get_model(args.arch, args.bins, inference_mode=True)

    if os.path.exists(best_model_path):
        model.load_state_dict(torch.load(best_model_path, map_location=device, weights_only=True))
    else:
        raise ValueError(f"Model weight not found at {best_model_path}")

    model.to(device)
    logging.info("Start Testing")
    evaluate(args, model, test_loader, idx_tensor, device, mode="test")


if __name__ == '__main__':
    main()

# From scratch another pipeline:  test/angular_error        4.6
# From author another pipeline:  test/angular_error         3.0

# INFO: Total Number of Samples: 142 | Angular Vector Error: 4.655°
# INFO: Total Number of Samples: 142 | Angular Vector Error: 4.359°
# INFO: Total Number of Samples: 142 | Angular Vector Error: 5.164°
# INFO: Total Number of Samples: 142 | Angular Vector Error: 4.967°
# INFO: Total Number of Samples: 142 | Angular Vector Error: 4.655°
# INFO: Total Number of Samples: 142 | Angular Vector Error: 4.6545652°